<?php
/**
 * Employee Model
 */

require_once 'BaseModel.php';

class Employee extends BaseModel {
    protected $table = 'employees';
    protected $fillable = [
        'user_id', 'employee_code', 'first_name', 'last_name', 'email', 
        'phone', 'address', 'birth_date', 'hire_date', 'position', 
        'department', 'manager_id', 'salary', 'status', 'profile_image', 'line_user_id'
    ];
    
    /**
     * Get employee with user data
     */
    public function getWithUser($id) {
        $sql = "SELECT e.*, u.username, u.role, u.is_active, u.last_login 
                FROM employees e 
                LEFT JOIN users u ON e.user_id = u.id 
                WHERE e.id = :id";
        
        return $this->db->fetchOne($sql, ['id' => $id]);
    }
    
    /**
     * Get all employees with user data
     */
    public function getAllWithUsers($conditions = [], $orderBy = 'e.first_name, e.last_name') {
        $sql = "SELECT e.*, u.username, u.role, u.is_active, u.last_login,
                       m.first_name as manager_first_name, m.last_name as manager_last_name
                FROM employees e 
                LEFT JOIN users u ON e.user_id = u.id 
                LEFT JOIN employees m ON e.manager_id = m.id";
        
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $field => $value) {
                $whereClause[] = "$field = :$field";
                $params[$field] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY $orderBy";
        }
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get employee by user ID
     */
    public function getByUserId($userId) {
        return $this->findFirst(['user_id' => $userId]);
    }
    
    /**
     * Get employee by employee code
     */
    public function getByEmployeeCode($code) {
        return $this->findFirst(['employee_code' => $code]);
    }
    
    /**
     * Get employees by department
     */
    public function getByDepartment($department) {
        return $this->findAll(['department' => $department, 'status' => 'active']);
    }
    
    /**
     * Get employees by manager
     */
    public function getByManager($managerId) {
        return $this->findAll(['manager_id' => $managerId, 'status' => 'active']);
    }
    
    /**
     * Search employees
     */
    public function search($query, $filters = []) {
        $sql = "SELECT e.*, u.username, u.role, u.is_active,
                       m.first_name as manager_first_name, m.last_name as manager_last_name
                FROM employees e 
                LEFT JOIN users u ON e.user_id = u.id 
                LEFT JOIN employees m ON e.manager_id = m.id
                WHERE (e.first_name LIKE :query 
                       OR e.last_name LIKE :query 
                       OR e.employee_code LIKE :query 
                       OR e.email LIKE :query 
                       OR e.position LIKE :query 
                       OR e.department LIKE :query)";
        
        $params = ['query' => "%$query%"];
        
        // Apply filters
        if (isset($filters['department']) && !empty($filters['department'])) {
            $sql .= " AND e.department = :department";
            $params['department'] = $filters['department'];
        }
        
        if (isset($filters['status']) && !empty($filters['status'])) {
            $sql .= " AND e.status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (isset($filters['position']) && !empty($filters['position'])) {
            $sql .= " AND e.position = :position";
            $params['position'] = $filters['position'];
        }
        
        $sql .= " ORDER BY e.first_name, e.last_name";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get departments list
     */
    public function getDepartments() {
        $sql = "SELECT DISTINCT department FROM employees WHERE department IS NOT NULL AND department != '' ORDER BY department";
        $results = $this->db->fetchAll($sql);
        
        return array_column($results, 'department');
    }
    
    /**
     * Get positions list
     */
    public function getPositions() {
        $sql = "SELECT DISTINCT position FROM employees WHERE position IS NOT NULL AND position != '' ORDER BY position";
        $results = $this->db->fetchAll($sql);
        
        return array_column($results, 'position');
    }
    
    /**
     * Get employee statistics
     */
    public function getStatistics() {
        $stats = [];
        
        // Total employees
        $stats['total'] = $this->count(['status' => 'active']);
        
        // By department
        $sql = "SELECT department, COUNT(*) as count 
                FROM employees 
                WHERE status = 'active' AND department IS NOT NULL 
                GROUP BY department 
                ORDER BY count DESC";
        $stats['by_department'] = $this->db->fetchAll($sql);
        
        // By position
        $sql = "SELECT position, COUNT(*) as count 
                FROM employees 
                WHERE status = 'active' AND position IS NOT NULL 
                GROUP BY position 
                ORDER BY count DESC";
        $stats['by_position'] = $this->db->fetchAll($sql);
        
        // New hires this month
        $sql = "SELECT COUNT(*) as count 
                FROM employees 
                WHERE status = 'active' 
                AND YEAR(hire_date) = YEAR(CURDATE()) 
                AND MONTH(hire_date) = MONTH(CURDATE())";
        $result = $this->db->fetchOne($sql);
        $stats['new_hires_this_month'] = $result['count'];
        
        // Birthdays this month
        $sql = "SELECT COUNT(*) as count 
                FROM employees 
                WHERE status = 'active' 
                AND MONTH(birth_date) = MONTH(CURDATE())";
        $result = $this->db->fetchOne($sql);
        $stats['birthdays_this_month'] = $result['count'];
        
        return $stats;
    }
    
    /**
     * Get upcoming birthdays
     */
    public function getUpcomingBirthdays($days = 30) {
        $sql = "SELECT id, first_name, last_name, birth_date, department, position
                FROM employees 
                WHERE status = 'active' 
                AND (
                    (DAYOFYEAR(birth_date) >= DAYOFYEAR(CURDATE()) 
                     AND DAYOFYEAR(birth_date) <= DAYOFYEAR(DATE_ADD(CURDATE(), INTERVAL :days DAY)))
                    OR 
                    (DAYOFYEAR(birth_date) <= DAYOFYEAR(DATE_ADD(CURDATE(), INTERVAL :days DAY)) - 365)
                )
                ORDER BY 
                    CASE 
                        WHEN DAYOFYEAR(birth_date) >= DAYOFYEAR(CURDATE()) 
                        THEN DAYOFYEAR(birth_date) 
                        ELSE DAYOFYEAR(birth_date) + 365 
                    END";
        
        return $this->db->fetchAll($sql, ['days' => $days]);
    }
    
    /**
     * Get work anniversaries
     */
    public function getWorkAnniversaries($days = 30) {
        $sql = "SELECT id, first_name, last_name, hire_date, department, position,
                       YEAR(CURDATE()) - YEAR(hire_date) as years_of_service
                FROM employees 
                WHERE status = 'active' 
                AND (
                    (DAYOFYEAR(hire_date) >= DAYOFYEAR(CURDATE()) 
                     AND DAYOFYEAR(hire_date) <= DAYOFYEAR(DATE_ADD(CURDATE(), INTERVAL :days DAY)))
                    OR 
                    (DAYOFYEAR(hire_date) <= DAYOFYEAR(DATE_ADD(CURDATE(), INTERVAL :days DAY)) - 365)
                )
                ORDER BY 
                    CASE 
                        WHEN DAYOFYEAR(hire_date) >= DAYOFYEAR(CURDATE()) 
                        THEN DAYOFYEAR(hire_date) 
                        ELSE DAYOFYEAR(hire_date) + 365 
                    END";
        
        return $this->db->fetchAll($sql, ['days' => $days]);
    }
    
    /**
     * Validate employee data
     */
    public function validateEmployee($data, $id = null) {
        $rules = [
            'employee_code' => 'required|unique:employees,employee_code' . ($id ? ",$id" : ''),
            'first_name' => 'required|max:100',
            'last_name' => 'required|max:100',
            'email' => 'required|email|unique:employees,email' . ($id ? ",$id" : ''),
            'phone' => 'phone',
            'birth_date' => 'date',
            'hire_date' => 'required|date',
            'position' => 'max:100',
            'department' => 'max:100',
            'salary' => 'numeric'
        ];
        
        return $this->validate($data, $rules);
    }
    
    /**
     * Create employee with user account
     */
    public function createWithUser($employeeData, $userData) {
        $this->beginTransaction();
        
        try {
            // Create user account first
            $userModel = new User();
            $user = $userModel->create($userData);
            
            if (!$user) {
                throw new Exception('Failed to create user account');
            }
            
            // Add user_id to employee data
            $employeeData['user_id'] = $user['id'];
            
            // Create employee record
            $employee = $this->create($employeeData);
            
            if (!$employee) {
                throw new Exception('Failed to create employee record');
            }
            
            $this->commit();
            return $employee;
            
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }
    
    /**
     * Get full name
     */
    public function getFullName($employee) {
        if (is_array($employee)) {
            return trim($employee['first_name'] . ' ' . $employee['last_name']);
        }
        return '';
    }
    
    /**
     * Calculate age
     */
    public function calculateAge($birthDate) {
        if (!$birthDate) return null;
        
        $birth = new DateTime($birthDate);
        $today = new DateTime();
        $age = $today->diff($birth);
        
        return $age->y;
    }
    
    /**
     * Calculate years of service
     */
    public function calculateYearsOfService($hireDate) {
        if (!$hireDate) return null;
        
        $hire = new DateTime($hireDate);
        $today = new DateTime();
        $service = $today->diff($hire);
        
        return $service->y;
    }
}
?>
