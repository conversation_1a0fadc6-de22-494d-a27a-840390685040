<?php
/**
 * User Model
 */

require_once 'BaseModel.php';

class User extends BaseModel {
    protected $table = 'users';
    protected $fillable = [
        'username', 'email', 'password_hash', 'role', 'is_active'
    ];
    protected $hidden = ['password_hash'];
    
    /**
     * Create user with hashed password
     */
    public function createUser($data) {
        if (isset($data['password'])) {
            $data['password_hash'] = Security::hashPassword($data['password']);
            unset($data['password']);
        }
        
        return $this->create($data);
    }
    
    /**
     * Update user password
     */
    public function updatePassword($id, $newPassword) {
        $hashedPassword = Security::hashPassword($newPassword);
        return $this->update($id, ['password_hash' => $hashedPassword]);
    }
    
    /**
     * Find user by username or email
     */
    public function findByUsernameOrEmail($identifier) {
        $sql = "SELECT * FROM {$this->table} WHERE username = :identifier OR email = :identifier";
        $result = $this->db->fetchOne($sql, ['identifier' => $identifier]);
        
        return $result ? $this->hideFields($result) : null;
    }
    
    /**
     * Validate user data
     */
    public function validateUser($data, $id = null) {
        $rules = [
            'username' => 'required|min:3|max:50|unique:users,username' . ($id ? ",$id" : ''),
            'email' => 'required|email|unique:users,email' . ($id ? ",$id" : ''),
            'role' => 'required'
        ];
        
        if (!$id || isset($data['password'])) {
            $rules['password'] = 'required|password';
        }
        
        return $this->validate($data, $rules);
    }
    
    /**
     * Get users by role
     */
    public function getByRole($role) {
        return $this->findAll(['role' => $role, 'is_active' => 1]);
    }
    
    /**
     * Lock user account
     */
    public function lockAccount($id, $lockUntil = null) {
        $lockUntil = $lockUntil ?: date('Y-m-d H:i:s', time() + Config::LOGIN_LOCKOUT_TIME);
        
        return $this->update($id, [
            'locked_until' => $lockUntil,
            'failed_login_attempts' => Config::MAX_LOGIN_ATTEMPTS
        ]);
    }
    
    /**
     * Unlock user account
     */
    public function unlockAccount($id) {
        return $this->update($id, [
            'locked_until' => null,
            'failed_login_attempts' => 0
        ]);
    }
    
    /**
     * Update last login
     */
    public function updateLastLogin($id) {
        return $this->update($id, ['last_login' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * Activate user
     */
    public function activate($id) {
        return $this->update($id, ['is_active' => 1]);
    }
    
    /**
     * Deactivate user
     */
    public function deactivate($id) {
        return $this->update($id, ['is_active' => 0]);
    }
}
?>
