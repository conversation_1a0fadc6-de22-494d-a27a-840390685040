<?php
/**
 * Test file to check server configuration
 */

echo "<h1>HRC System Test</h1>";

// Check PHP version
echo "<h2>PHP Information</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Server: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";

// Check required extensions
echo "<h2>Required Extensions</h2>";
$required_extensions = ['pdo', 'pdo_mysql', 'openssl', 'json', 'mbstring'];
foreach ($required_extensions as $ext) {
    $status = extension_loaded($ext) ? '✅ Loaded' : '❌ Missing';
    echo "$ext: $status<br>";
}

// Check mod_rewrite
echo "<h2>Apache Modules</h2>";
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    $mod_rewrite = in_array('mod_rewrite', $modules) ? '✅ Enabled' : '❌ Disabled';
    echo "mod_rewrite: $mod_rewrite<br>";
    
    $mod_headers = in_array('mod_headers', $modules) ? '✅ Enabled' : '❌ Disabled';
    echo "mod_headers: $mod_headers<br>";
} else {
    echo "Cannot check Apache modules (not running on Apache or function disabled)<br>";
}

// Check file permissions
echo "<h2>File Permissions</h2>";
$directories = ['Assets', 'Logs', 'Config'];
foreach ($directories as $dir) {
    if (is_dir($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        $writable = is_writable($dir) ? '✅ Writable' : '❌ Not writable';
        echo "$dir: $perms ($writable)<br>";
    } else {
        echo "$dir: ❌ Directory not found<br>";
    }
}

// Check .htaccess
echo "<h2>.htaccess File</h2>";
if (file_exists('.htaccess')) {
    echo "✅ .htaccess file exists<br>";
    echo "Size: " . filesize('.htaccess') . " bytes<br>";
} else {
    echo "❌ .htaccess file not found<br>";
}

// Test URL rewriting
echo "<h2>URL Rewriting Test</h2>";
echo "Current URL: " . $_SERVER['REQUEST_URI'] . "<br>";
echo "Query String: " . ($_SERVER['QUERY_STRING'] ?? 'None') . "<br>";

if (isset($_GET['url'])) {
    echo "URL parameter: " . $_GET['url'] . "<br>";
    echo "✅ URL rewriting is working<br>";
} else {
    echo "❌ URL rewriting may not be working<br>";
    echo "Try accessing: <a href='test.php/sample/url'>test.php/sample/url</a><br>";
}

// Database connection test
echo "<h2>Database Connection Test</h2>";
try {
    // Include database config
    if (file_exists('Config/Database.php')) {
        require_once 'Config/Database.php';
        $db = Database::getInstance();
        echo "✅ Database connection successful<br>";
        
        // Test query
        $result = $db->query("SELECT 1 as test");
        if ($result) {
            echo "✅ Database query test successful<br>";
        }
    } else {
        echo "❌ Database config file not found<br>";
    }
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
}

// Check if index.php exists
echo "<h2>Main Files</h2>";
$main_files = ['index.php', 'Config/Config.php', 'Config/Database.php'];
foreach ($main_files as $file) {
    $status = file_exists($file) ? '✅ Exists' : '❌ Missing';
    echo "$file: $status<br>";
}

echo "<hr>";
echo "<h2>Quick Links</h2>";
echo "<a href='index.php'>Direct index.php</a><br>";
echo "<a href='auth/login'>Login Page (with rewrite)</a><br>";
echo "<a href='index.php?url=auth/login'>Login Page (without rewrite)</a><br>";

echo "<hr>";
echo "<p><strong>If you see 'URL rewriting is working' above, the system should work properly.</strong></p>";
echo "<p>If not, please check:</p>";
echo "<ul>";
echo "<li>Apache mod_rewrite is enabled</li>";
echo "<li>AllowOverride is set to All in Apache config</li>";
echo "<li>.htaccess file has correct permissions</li>";
echo "</ul>";
?>
