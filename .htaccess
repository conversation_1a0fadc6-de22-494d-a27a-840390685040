# Check if mod_rewrite is available
<IfModule mod_rewrite.c>
    RewriteEngine On

    # Set base directory (adjust if needed)
    # RewriteBase /hrc/

    # Prevent access to sensitive directories
    RewriteRule ^(Config|Models|Controllers|Helpers|Middleware|Logs)/ - [F,L]

    # Redirect to HTTPS (uncomment if needed)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # Handle static assets
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteRule ^Assets/(.*)$ Assets/$1 [L]

    # Main routing - redirect everything to index.php
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ index.php?url=$1 [QSA,L]
</IfModule>

# Fallback for servers without mod_rewrite
<IfModule !mod_rewrite.c>
    # Redirect to index.php with error
    ErrorDocument 404 /index.php?url=404
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=********; includeSubDomains"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Disable server signature
ServerSignature Off

# Hide PHP version
<IfModule mod_headers.c>
    Header unset X-Powered-By
</IfModule>
