<?php
/**
 * Base Controller
 * Parent class for all controllers
 */

class BaseController {
    protected $db;
    protected $logger;
    protected $auth;
    protected $data = [];
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->logger = new Logger();
        $this->auth = new AuthMiddleware();
        
        // Set default data available to all views
        $this->data['current_user'] = $this->auth->getCurrentUser();
        $this->data['csrf_token'] = Security::generateCSRFToken();
        $this->data['base_url'] = Config::getBaseUrl();
        $this->data['app_name'] = Config::APP_NAME;
    }
    
    /**
     * Load and render view
     */
    protected function view($viewPath, $data = []) {
        // Merge controller data with view data
        $data = array_merge($this->data, $data);
        
        // Extract data to variables
        extract($data);
        
        // Check if view file exists
        $viewFile = VIEWS_PATH . '/' . $viewPath . '.php';
        if (!file_exists($viewFile)) {
            throw new Exception("View not found: $viewPath");
        }
        
        // Load view
        include $viewFile;
    }
    
    /**
     * Return JSON response
     */
    protected function json($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
    
    /**
     * Return success JSON response
     */
    protected function jsonSuccess($message = 'Success', $data = []) {
        $this->json([
            'success' => true,
            'message' => $message,
            'data' => $data
        ]);
    }
    
    /**
     * Return error JSON response
     */
    protected function jsonError($message = 'Error', $errors = [], $statusCode = 400) {
        $this->json([
            'success' => false,
            'message' => $message,
            'errors' => $errors
        ], $statusCode);
    }
    
    /**
     * Redirect to URL
     */
    protected function redirect($url, $message = null, $type = 'info') {
        if ($message) {
            $_SESSION['flash_message'] = $message;
            $_SESSION['flash_type'] = $type;
        }
        
        header("Location: $url");
        exit;
    }
    
    /**
     * Get POST data with sanitization
     */
    protected function getPostData($keys = null) {
        $data = [];
        
        if ($keys === null) {
            $keys = array_keys($_POST);
        }
        
        foreach ($keys as $key) {
            if (isset($_POST[$key])) {
                $data[$key] = Security::sanitizeInput($_POST[$key]);
            }
        }
        
        return $data;
    }
    
    /**
     * Get GET data with sanitization
     */
    protected function getGetData($keys = null) {
        $data = [];
        
        if ($keys === null) {
            $keys = array_keys($_GET);
        }
        
        foreach ($keys as $key) {
            if (isset($_GET[$key])) {
                $data[$key] = Security::sanitizeInput($_GET[$key]);
            }
        }
        
        return $data;
    }
    
    /**
     * Validate CSRF token
     */
    protected function validateCSRF() {
        return $this->auth->checkCSRF();
    }
    
    /**
     * Check if user is authenticated
     */
    protected function requireAuth() {
        return $this->auth->requireAuth();
    }
    
    /**
     * Check if user has required role
     */
    protected function requireRole($role) {
        return $this->auth->requireRole($role);
    }
    
    /**
     * Check if user is admin
     */
    protected function requireAdmin() {
        return $this->auth->requireAdmin();
    }
    
    /**
     * Check if user is HR
     */
    protected function requireHR() {
        return $this->auth->requireHR();
    }
    
    /**
     * Handle file upload
     */
    protected function handleFileUpload($fileKey, $uploadPath = null) {
        if (!isset($_FILES[$fileKey]) || $_FILES[$fileKey]['error'] === UPLOAD_ERR_NO_FILE) {
            return null;
        }
        
        $file = $_FILES[$fileKey];
        
        // Validate file
        $errors = Security::validateFileUpload($file);
        if (!empty($errors)) {
            throw new Exception(implode(', ', $errors));
        }
        
        // Set upload path
        $uploadPath = $uploadPath ?: Config::UPLOAD_PATH;
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }
        
        // Generate secure filename
        $filename = Security::generateSecureFilename($file['name']);
        $filePath = $uploadPath . $filename;
        
        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $filePath)) {
            throw new Exception('Failed to upload file');
        }
        
        return $filename;
    }
    
    /**
     * Paginate results
     */
    protected function paginate($query, $params = [], $page = 1, $perPage = null) {
        $perPage = $perPage ?: Config::ITEMS_PER_PAGE;
        $offset = ($page - 1) * $perPage;
        
        // Get total count
        $countQuery = "SELECT COUNT(*) as total FROM ($query) as count_table";
        $totalResult = $this->db->fetchOne($countQuery, $params);
        $total = $totalResult['total'];
        
        // Get paginated results
        $paginatedQuery = $query . " LIMIT $perPage OFFSET $offset";
        $results = $this->db->fetchAll($paginatedQuery, $params);
        
        return [
            'data' => $results,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => ceil($total / $perPage),
                'has_next' => $page < ceil($total / $perPage),
                'has_prev' => $page > 1
            ]
        ];
    }
    
    /**
     * Log user activity
     */
    protected function logActivity($action, $details = '') {
        $this->logger->logActivity($action, $details);
    }
    
    /**
     * Set flash message
     */
    protected function setFlash($message, $type = 'info') {
        $_SESSION['flash_message'] = $message;
        $_SESSION['flash_type'] = $type;
    }
    
    /**
     * Get and clear flash message
     */
    protected function getFlash() {
        $message = $_SESSION['flash_message'] ?? null;
        $type = $_SESSION['flash_type'] ?? 'info';
        
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        
        return $message ? ['message' => $message, 'type' => $type] : null;
    }
    
    /**
     * Check if request is AJAX
     */
    protected function isAjax() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * Format date for display
     */
    protected function formatDate($date, $format = null) {
        if (!$date) return '';
        
        $format = $format ?: Config::DISPLAY_DATE_FORMAT;
        return date($format, strtotime($date));
    }
    
    /**
     * Format datetime for display
     */
    protected function formatDateTime($datetime, $format = null) {
        if (!$datetime) return '';
        
        $format = $format ?: Config::DISPLAY_DATETIME_FORMAT;
        return date($format, strtotime($datetime));
    }
}
?>
