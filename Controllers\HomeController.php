<?php
/**
 * Home Controller
 * Handles main application routing and dashboard
 */

require_once 'BaseController.php';

class HomeController extends BaseController {
    
    /**
     * Default index page
     */
    public function index() {
        // Redirect based on authentication status
        if (Security::isLoggedIn()) {
            $userRole = $_SESSION['user_role'];
            
            if ($userRole === 'admin' || $userRole === 'hr') {
                $this->redirect('/admin/dashboard');
            } else {
                $this->redirect('/dashboard');
            }
        } else {
            $this->redirect('/auth/login');
        }
    }
    
    /**
     * Employee dashboard
     */
    public function dashboard() {
        if (!$this->requireAuth()) {
            return;
        }
        
        try {
            $currentUser = $this->auth->getCurrentUser();
            
            if (!$currentUser || !$currentUser['employee_id']) {
                $this->setFlash('Employee profile not found', 'error');
                $this->redirect('/auth/logout');
            }
            
            $employeeId = $currentUser['employee_id'];
            
            // Load models
            $employeeModel = new Employee();
            $leaveModel = new LeaveRequest();
            $salaryModel = new SalaryRecord();
            
            // Get employee data
            $employee = $employeeModel->getWithUser($employeeId);
            
            // Get leave balance
            $leaveBalance = $leaveModel->getAllLeaveBalances($employeeId);
            
            // Get recent leave requests
            $recentLeaves = $leaveModel->getByEmployee($employeeId, date('Y'));
            $recentLeaves = array_slice($recentLeaves, 0, 5); // Last 5 requests
            
            // Get recent salary records
            $recentSalaries = $salaryModel->getByEmployee($employeeId, 3);
            
            // Get pending leave requests count
            $pendingLeavesCount = count(array_filter($recentLeaves, function($leave) {
                return $leave['status'] === 'pending';
            }));
            
            $this->data['page_title'] = 'Dashboard';
            $this->data['employee'] = $employee;
            $this->data['leave_balance'] = $leaveBalance;
            $this->data['recent_leaves'] = $recentLeaves;
            $this->data['recent_salaries'] = $recentSalaries;
            $this->data['pending_leaves_count'] = $pendingLeavesCount;
            $this->data['flash'] = $this->getFlash();
            
            $this->view('user/dashboard');
            
        } catch (Exception $e) {
            $this->logger->logError('Dashboard error: ' . $e->getMessage());
            $this->setFlash('An error occurred while loading dashboard', 'error');
            $this->view('user/dashboard');
        }
    }
    
    /**
     * Employee profile
     */
    public function profile() {
        if (!$this->requireAuth()) {
            return;
        }
        
        try {
            $currentUser = $this->auth->getCurrentUser();
            $employeeId = $currentUser['employee_id'];
            
            $employeeModel = new Employee();
            $employee = $employeeModel->getWithUser($employeeId);
            
            if (!$employee) {
                $this->setFlash('Employee profile not found', 'error');
                $this->redirect('/dashboard');
            }
            
            $this->data['page_title'] = 'My Profile';
            $this->data['employee'] = $employee;
            $this->data['flash'] = $this->getFlash();
            
            $this->view('user/profile');
            
        } catch (Exception $e) {
            $this->logger->logError('Profile error: ' . $e->getMessage());
            $this->setFlash('An error occurred while loading profile', 'error');
            $this->redirect('/dashboard');
        }
    }
    
    /**
     * Update profile
     */
    public function updateProfile() {
        if (!$this->requireAuth()) {
            return;
        }
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/profile');
        }
        
        $this->validateCSRF();
        
        try {
            $currentUser = $this->auth->getCurrentUser();
            $employeeId = $currentUser['employee_id'];
            
            $data = $this->getPostData(['phone', 'address']);
            
            // Validate data
            $validator = Validator::make($data, [
                'phone' => 'phone',
                'address' => 'max:500'
            ]);
            
            if ($validator->fails()) {
                if ($this->isAjax()) {
                    $this->jsonError('Validation failed', $validator->getErrors());
                }
                
                $this->setFlash('Please correct the errors and try again', 'error');
                $this->redirect('/profile');
            }
            
            // Handle profile image upload
            if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
                $filename = $this->handleFileUpload('profile_image', 'Assets/uploads/profiles/');
                $data['profile_image'] = $filename;
                
                // Delete old profile image
                $employeeModel = new Employee();
                $employee = $employeeModel->find($employeeId);
                if ($employee['profile_image'] && file_exists('Assets/uploads/profiles/' . $employee['profile_image'])) {
                    unlink('Assets/uploads/profiles/' . $employee['profile_image']);
                }
            }
            
            // Update employee
            $employeeModel = new Employee();
            $updated = $employeeModel->update($employeeId, $data);
            
            if ($updated) {
                $this->logActivity('Profile updated', 'Employee updated their profile');
                
                if ($this->isAjax()) {
                    $this->jsonSuccess('Profile updated successfully');
                }
                
                $this->setFlash('Profile updated successfully', 'success');
            } else {
                if ($this->isAjax()) {
                    $this->jsonError('Failed to update profile');
                }
                
                $this->setFlash('Failed to update profile', 'error');
            }
            
            $this->redirect('/profile');
            
        } catch (Exception $e) {
            $this->logger->logError('Profile update error: ' . $e->getMessage());
            
            if ($this->isAjax()) {
                $this->jsonError('An error occurred while updating profile');
            }
            
            $this->setFlash('An error occurred while updating profile', 'error');
            $this->redirect('/profile');
        }
    }
    
    /**
     * Change password
     */
    public function changePassword() {
        if (!$this->requireAuth()) {
            return;
        }
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/profile');
        }
        
        $this->validateCSRF();
        
        try {
            $data = $this->getPostData(['current_password', 'new_password', 'confirm_password']);
            
            // Validate data
            $validator = Validator::make($data, [
                'current_password' => 'required',
                'new_password' => 'required|password',
                'confirm_password' => 'required'
            ]);
            
            if ($validator->fails()) {
                if ($this->isAjax()) {
                    $this->jsonError('Validation failed', $validator->getErrors());
                }
                
                $this->setFlash('Please correct the errors and try again', 'error');
                $this->redirect('/profile');
            }
            
            // Check password confirmation
            if ($data['new_password'] !== $data['confirm_password']) {
                if ($this->isAjax()) {
                    $this->jsonError('Password confirmation does not match');
                }
                
                $this->setFlash('Password confirmation does not match', 'error');
                $this->redirect('/profile');
            }
            
            // Verify current password
            $userModel = new User();
            $user = $userModel->find($_SESSION['user_id']);
            
            if (!$user || !Security::verifyPassword($data['current_password'], $user['password_hash'])) {
                if ($this->isAjax()) {
                    $this->jsonError('Current password is incorrect');
                }
                
                $this->setFlash('Current password is incorrect', 'error');
                $this->redirect('/profile');
            }
            
            // Update password
            $updated = $userModel->updatePassword($_SESSION['user_id'], $data['new_password']);
            
            if ($updated) {
                $this->logActivity('Password changed', 'User changed their password');
                
                if ($this->isAjax()) {
                    $this->jsonSuccess('Password changed successfully');
                }
                
                $this->setFlash('Password changed successfully', 'success');
            } else {
                if ($this->isAjax()) {
                    $this->jsonError('Failed to change password');
                }
                
                $this->setFlash('Failed to change password', 'error');
            }
            
            $this->redirect('/profile');
            
        } catch (Exception $e) {
            $this->logger->logError('Change password error: ' . $e->getMessage());
            
            if ($this->isAjax()) {
                $this->jsonError('An error occurred while changing password');
            }
            
            $this->setFlash('An error occurred while changing password', 'error');
            $this->redirect('/profile');
        }
    }
    
    /**
     * Show 404 error page
     */
    public function notFound() {
        http_response_code(404);
        $this->data['page_title'] = 'Page Not Found';
        $this->view('errors/404');
    }
    
    /**
     * Show 403 error page
     */
    public function accessDenied() {
        http_response_code(403);
        $this->data['page_title'] = 'Access Denied';
        $this->view('errors/403');
    }
    
    /**
     * Show 500 error page
     */
    public function serverError() {
        http_response_code(500);
        $this->data['page_title'] = 'Server Error';
        $this->view('errors/500');
    }
}
?>
