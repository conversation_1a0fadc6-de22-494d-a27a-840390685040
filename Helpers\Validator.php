<?php
/**
 * Validation Helper Class
 * Handles form validation and data validation
 */

class Validator {
    private $errors = [];
    private $data = [];
    
    public function __construct($data = []) {
        $this->data = $data;
        $this->errors = [];
    }
    
    /**
     * Validate required field
     */
    public function required($field, $message = null) {
        $message = $message ?: ucfirst($field) . ' is required';
        
        if (!isset($this->data[$field]) || empty(trim($this->data[$field]))) {
            $this->errors[$field][] = $message;
        }
        
        return $this;
    }
    
    /**
     * Validate email format
     */
    public function email($field, $message = null) {
        $message = $message ?: 'Please enter a valid email address';
        
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!Security::validateEmail($this->data[$field])) {
                $this->errors[$field][] = $message;
            }
        }
        
        return $this;
    }
    
    /**
     * Validate minimum length
     */
    public function minLength($field, $length, $message = null) {
        $message = $message ?: ucfirst($field) . " must be at least $length characters long";
        
        if (isset($this->data[$field]) && strlen($this->data[$field]) < $length) {
            $this->errors[$field][] = $message;
        }
        
        return $this;
    }
    
    /**
     * Validate maximum length
     */
    public function maxLength($field, $length, $message = null) {
        $message = $message ?: ucfirst($field) . " must not exceed $length characters";
        
        if (isset($this->data[$field]) && strlen($this->data[$field]) > $length) {
            $this->errors[$field][] = $message;
        }
        
        return $this;
    }
    
    /**
     * Validate numeric value
     */
    public function numeric($field, $message = null) {
        $message = $message ?: ucfirst($field) . ' must be a number';
        
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!is_numeric($this->data[$field])) {
                $this->errors[$field][] = $message;
            }
        }
        
        return $this;
    }
    
    /**
     * Validate date format
     */
    public function date($field, $format = 'Y-m-d', $message = null) {
        $message = $message ?: ucfirst($field) . ' must be a valid date';
        
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $date = DateTime::createFromFormat($format, $this->data[$field]);
            if (!$date || $date->format($format) !== $this->data[$field]) {
                $this->errors[$field][] = $message;
            }
        }
        
        return $this;
    }
    
    /**
     * Validate phone number (Thai format)
     */
    public function phone($field, $message = null) {
        $message = $message ?: 'Please enter a valid phone number';
        
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $phone = preg_replace('/[^0-9]/', '', $this->data[$field]);
            if (!preg_match('/^(0[689]{1}[0-9]{8}|0[2-7]{1}[0-9]{7})$/', $phone)) {
                $this->errors[$field][] = $message;
            }
        }
        
        return $this;
    }
    
    /**
     * Validate password strength
     */
    public function password($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $passwordErrors = Security::validatePassword($this->data[$field]);
            if (!empty($passwordErrors)) {
                $this->errors[$field] = array_merge($this->errors[$field] ?? [], $passwordErrors);
            }
        }
        
        return $this;
    }
    
    /**
     * Validate unique value in database
     */
    public function unique($field, $table, $column = null, $excludeId = null, $message = null) {
        $column = $column ?: $field;
        $message = $message ?: ucfirst($field) . ' already exists';
        
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $db = Database::getInstance();
            
            $sql = "SELECT COUNT(*) as count FROM $table WHERE $column = :value";
            $params = ['value' => $this->data[$field]];
            
            if ($excludeId) {
                $sql .= " AND id != :exclude_id";
                $params['exclude_id'] = $excludeId;
            }
            
            $result = $db->fetchOne($sql, $params);
            
            if ($result['count'] > 0) {
                $this->errors[$field][] = $message;
            }
        }
        
        return $this;
    }
    
    /**
     * Check if validation passed
     */
    public function passes() {
        return empty($this->errors);
    }
    
    /**
     * Check if validation failed
     */
    public function fails() {
        return !$this->passes();
    }
    
    /**
     * Get all errors
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * Get errors for specific field
     */
    public function getError($field) {
        return $this->errors[$field] ?? [];
    }
    
    /**
     * Get first error for field
     */
    public function getFirstError($field) {
        $errors = $this->getError($field);
        return !empty($errors) ? $errors[0] : null;
    }
    
    /**
     * Static method for quick validation
     */
    public static function make($data, $rules) {
        $validator = new self($data);
        
        foreach ($rules as $field => $fieldRules) {
            $fieldRules = is_string($fieldRules) ? explode('|', $fieldRules) : $fieldRules;
            
            foreach ($fieldRules as $rule) {
                if (strpos($rule, ':') !== false) {
                    list($ruleName, $ruleValue) = explode(':', $rule, 2);
                } else {
                    $ruleName = $rule;
                    $ruleValue = null;
                }
                
                switch ($ruleName) {
                    case 'required':
                        $validator->required($field);
                        break;
                    case 'email':
                        $validator->email($field);
                        break;
                    case 'min':
                        $validator->minLength($field, (int)$ruleValue);
                        break;
                    case 'max':
                        $validator->maxLength($field, (int)$ruleValue);
                        break;
                    case 'numeric':
                        $validator->numeric($field);
                        break;
                    case 'date':
                        $validator->date($field);
                        break;
                    case 'phone':
                        $validator->phone($field);
                        break;
                    case 'password':
                        $validator->password($field);
                        break;
                    case 'unique':
                        if ($ruleValue) {
                            $parts = explode(',', $ruleValue);
                            $table = $parts[0];
                            $column = $parts[1] ?? $field;
                            $excludeId = $parts[2] ?? null;
                            $validator->unique($field, $table, $column, $excludeId);
                        }
                        break;
                }
            }
        }
        
        return $validator;
    }
}
?>
