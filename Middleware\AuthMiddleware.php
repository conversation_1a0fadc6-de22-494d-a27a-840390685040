<?php
/**
 * Authentication Middleware
 * Handles user authentication and authorization
 */

class AuthMiddleware {
    private $logger;
    
    public function __construct() {
        $this->logger = new Logger();
    }
    
    /**
     * Check if user is authenticated
     */
    public function requireAuth() {
        if (!Security::isLoggedIn()) {
            $this->logger->logSecurity('Unauthorized access attempt', 'User not logged in');
            $this->redirectToLogin();
            return false;
        }
        
        // Check session timeout
        if (!Security::checkSessionTimeout()) {
            $this->redirectToLogin('Session expired. Please login again.');
            return false;
        }
        
        return true;
    }
    
    /**
     * Check if user has required role
     */
    public function requireRole($requiredRole) {
        if (!$this->requireAuth()) {
            return false;
        }
        
        if (!Security::hasRole($requiredRole)) {
            $this->logger->logSecurity('Access denied', "User role: {$_SESSION['user_role']}, Required: $requiredRole");
            $this->accessDenied();
            return false;
        }
        
        return true;
    }
    
    /**
     * Check if user is admin
     */
    public function requireAdmin() {
        return $this->requireRole('admin');
    }
    
    /**
     * Check if user is HR or admin
     */
    public function requireHR() {
        return $this->requireRole('hr');
    }
    
    /**
     * Check if user is employee or higher
     */
    public function requireEmployee() {
        return $this->requireRole('employee');
    }
    
    /**
     * Check if user can access employee data
     */
    public function canAccessEmployee($employeeId) {
        if (!$this->requireAuth()) {
            return false;
        }
        
        $userRole = $_SESSION['user_role'];
        $userId = $_SESSION['user_id'];
        
        // Admin and HR can access all employees
        if (in_array($userRole, ['admin', 'hr'])) {
            return true;
        }
        
        // Employee can only access their own data
        if ($userRole === 'employee') {
            $db = Database::getInstance();
            $employee = $db->fetchOne(
                "SELECT id FROM employees WHERE user_id = :user_id",
                ['user_id' => $userId]
            );
            
            return $employee && $employee['id'] == $employeeId;
        }
        
        return false;
    }
    
    /**
     * Redirect to login page
     */
    private function redirectToLogin($message = null) {
        if ($message) {
            $_SESSION['login_message'] = $message;
        }
        
        // If AJAX request, return JSON response
        if ($this->isAjaxRequest()) {
            http_response_code(401);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'Authentication required',
                'redirect' => '/auth/login'
            ]);
            exit;
        }
        
        // Regular redirect
        header('Location: /auth/login');
        exit;
    }
    
    /**
     * Show access denied page
     */
    private function accessDenied() {
        // If AJAX request, return JSON response
        if ($this->isAjaxRequest()) {
            http_response_code(403);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'Access denied. Insufficient permissions.'
            ]);
            exit;
        }
        
        // Show access denied page
        http_response_code(403);
        include VIEWS_PATH . '/errors/403.php';
        exit;
    }
    
    /**
     * Check if request is AJAX
     */
    private function isAjaxRequest() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * Get current user data
     */
    public function getCurrentUser() {
        if (!Security::isLoggedIn()) {
            return null;
        }
        
        $db = Database::getInstance();
        $user = $db->fetchOne(
            "SELECT u.*, e.id as employee_id, e.first_name, e.last_name, e.employee_code 
             FROM users u 
             LEFT JOIN employees e ON u.id = e.user_id 
             WHERE u.id = :user_id",
            ['user_id' => $_SESSION['user_id']]
        );
        
        return $user;
    }
    
    /**
     * Check CSRF token for POST requests
     */
    public function checkCSRF() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $token = $_POST['csrf_token'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? '';
            
            if (!Security::verifyCSRFToken($token)) {
                $this->logger->logSecurity('CSRF token validation failed', 'Invalid or missing CSRF token');
                
                if ($this->isAjaxRequest()) {
                    http_response_code(403);
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => false,
                        'message' => 'Invalid security token. Please refresh the page.'
                    ]);
                    exit;
                }
                
                die('Invalid security token. Please refresh the page and try again.');
            }
        }
        
        return true;
    }
    
    /**
     * Rate limiting middleware
     */
    public function rateLimit($identifier = null, $maxAttempts = 60, $timeWindow = 3600) {
        $identifier = $identifier ?: $_SERVER['REMOTE_ADDR'];
        $key = 'rate_limit_' . md5($identifier);
        
        $attempts = $_SESSION[$key] ?? [];
        $now = time();
        
        // Remove old attempts outside time window
        $attempts = array_filter($attempts, function($timestamp) use ($now, $timeWindow) {
            return ($now - $timestamp) < $timeWindow;
        });
        
        if (count($attempts) >= $maxAttempts) {
            $this->logger->logSecurity('Rate limit exceeded', "IP: $identifier, Attempts: " . count($attempts));
            
            if ($this->isAjaxRequest()) {
                http_response_code(429);
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => 'Too many requests. Please try again later.'
                ]);
                exit;
            }
            
            http_response_code(429);
            die('Too many requests. Please try again later.');
        }
        
        // Add current attempt
        $attempts[] = $now;
        $_SESSION[$key] = $attempts;
        
        return true;
    }
}
?>
