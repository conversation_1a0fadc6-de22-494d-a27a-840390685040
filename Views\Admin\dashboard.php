<?php
$content = ob_start();
?>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Employees
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= $employee_stats['total'] ?? 0 ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            New Hires This Month
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= $employee_stats['new_hires_this_month'] ?? 0 ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-plus fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Pending Leave Requests
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= count($pending_leaves ?? []) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-calendar-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Birthdays This Month
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= $employee_stats['birthdays_this_month'] ?? 0 ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-gift fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Department Distribution -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Employees by Department</h6>
            </div>
            <div class="card-body">
                <?php if (!empty($employee_stats['by_department'])): ?>
                    <canvas id="departmentChart"></canvas>
                <?php else: ?>
                    <p class="text-muted text-center">No data available</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Monthly Salary Overview -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Monthly Payroll</h6>
            </div>
            <div class="card-body">
                <?php if (!empty($salary_stats['monthly_breakdown'])): ?>
                    <canvas id="salaryChart"></canvas>
                <?php else: ?>
                    <p class="text-muted text-center">No data available</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Tables Row -->
<div class="row">
    <!-- Pending Leave Requests -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Pending Leave Requests</h6>
                <a href="/admin/leaves" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body">
                <?php if (!empty($pending_leaves)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Leave Type</th>
                                    <th>Dates</th>
                                    <th>Days</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($pending_leaves, 0, 5) as $leave): ?>
                                <tr>
                                    <td><?= Security::escapeOutput($leave['first_name'] . ' ' . $leave['last_name']) ?></td>
                                    <td><?= Security::escapeOutput($leave['leave_type_name']) ?></td>
                                    <td><?= date('d/m/Y', strtotime($leave['start_date'])) ?> - <?= date('d/m/Y', strtotime($leave['end_date'])) ?></td>
                                    <td><?= $leave['days_requested'] ?></td>
                                    <td>
                                        <a href="/admin/leaves/<?= $leave['id'] ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-muted text-center">No pending leave requests</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Upcoming Birthdays -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Upcoming Birthdays</h6>
            </div>
            <div class="card-body">
                <?php if (!empty($upcoming_birthdays)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Department</th>
                                    <th>Birthday</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($upcoming_birthdays, 0, 5) as $birthday): ?>
                                <tr>
                                    <td><?= Security::escapeOutput($birthday['first_name'] . ' ' . $birthday['last_name']) ?></td>
                                    <td><?= Security::escapeOutput($birthday['department']) ?></td>
                                    <td><?= date('d/m', strtotime($birthday['birth_date'])) ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-muted text-center">No upcoming birthdays</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Work Anniversaries -->
<?php if (!empty($work_anniversaries)): ?>
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Work Anniversaries This Month</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Employee</th>
                                <th>Department</th>
                                <th>Position</th>
                                <th>Anniversary Date</th>
                                <th>Years of Service</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($work_anniversaries as $anniversary): ?>
                            <tr>
                                <td><?= Security::escapeOutput($anniversary['first_name'] . ' ' . $anniversary['last_name']) ?></td>
                                <td><?= Security::escapeOutput($anniversary['department']) ?></td>
                                <td><?= Security::escapeOutput($anniversary['position']) ?></td>
                                <td><?= date('d/m/Y', strtotime($anniversary['hire_date'])) ?></td>
                                <td><?= $anniversary['years_of_service'] ?> years</td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Department Chart
<?php if (!empty($employee_stats['by_department'])): ?>
const departmentCtx = document.getElementById('departmentChart').getContext('2d');
const departmentChart = new Chart(departmentCtx, {
    type: 'doughnut',
    data: {
        labels: <?= json_encode(array_column($employee_stats['by_department'], 'department')) ?>,
        datasets: [{
            data: <?= json_encode(array_column($employee_stats['by_department'], 'count')) ?>,
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
                '#FF9F40'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
<?php endif; ?>

// Salary Chart
<?php if (!empty($salary_stats['monthly_breakdown'])): ?>
const salaryCtx = document.getElementById('salaryChart').getContext('2d');
const salaryChart = new Chart(salaryCtx, {
    type: 'line',
    data: {
        labels: <?= json_encode(array_map(function($item) { 
            return date('M', mktime(0, 0, 0, $item['month'], 1)); 
        }, $salary_stats['monthly_breakdown'])) ?>,
        datasets: [{
            label: 'Total Payroll',
            data: <?= json_encode(array_column($salary_stats['monthly_breakdown'], 'total')) ?>,
            borderColor: '#36A2EB',
            backgroundColor: 'rgba(54, 162, 235, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '฿' + value.toLocaleString();
                    }
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return 'Total: ฿' + context.parsed.y.toLocaleString();
                    }
                }
            }
        }
    }
});
<?php endif; ?>
</script>

<?php
$content = ob_get_clean();
include VIEWS_PATH . '/layouts/admin.php';
?>
