<?php
/**
 * Security Helper Class
 * Handles password hashing, encryption, CSRF protection, etc.
 */

class Security {
    private static $logger;
    
    public function __construct() {
        if (!self::$logger) {
            self::$logger = new Logger();
        }
    }
    
    /**
     * Hash password
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * Verify password
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * Generate secure random token
     */
    public static function generateToken($length = 32) {
        return bin2hex(random_bytes($length));
    }
    
    /**
     * Generate CSRF token
     */
    public static function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = self::generateToken();
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Verify CSRF token
     */
    public static function verifyCSRFToken($token) {
        if (!isset($_SESSION['csrf_token'])) {
            return false;
        }
        
        $isValid = hash_equals($_SESSION['csrf_token'], $token);
        
        if (!$isValid) {
            self::$logger->logSecurity('CSRF token mismatch', 'Invalid CSRF token provided');
        }
        
        return $isValid;
    }
    
    /**
     * Sanitize input
     */
    public static function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitizeInput'], $input);
        }
        
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Validate email format
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Validate password strength
     */
    public static function validatePassword($password) {
        $errors = [];
        
        if (strlen($password) < Config::PASSWORD_MIN_LENGTH) {
            $errors[] = "Password must be at least " . Config::PASSWORD_MIN_LENGTH . " characters long";
        }
        
        if (Config::PASSWORD_REQUIRE_UPPERCASE && !preg_match('/[A-Z]/', $password)) {
            $errors[] = "Password must contain at least one uppercase letter";
        }
        
        if (Config::PASSWORD_REQUIRE_LOWERCASE && !preg_match('/[a-z]/', $password)) {
            $errors[] = "Password must contain at least one lowercase letter";
        }
        
        if (Config::PASSWORD_REQUIRE_NUMBERS && !preg_match('/[0-9]/', $password)) {
            $errors[] = "Password must contain at least one number";
        }
        
        if (Config::PASSWORD_REQUIRE_SYMBOLS && !preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = "Password must contain at least one special character";
        }
        
        return $errors;
    }
    
    /**
     * Encrypt sensitive data
     */
    public static function encrypt($data) {
        $key = Config::ENCRYPTION_KEY;
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * Decrypt sensitive data
     */
    public static function decrypt($encryptedData) {
        $key = Config::ENCRYPTION_KEY;
        $data = base64_decode($encryptedData);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
    }
    
    /**
     * Check if user is logged in
     */
    public static function isLoggedIn() {
        return isset($_SESSION['user_id']) && isset($_SESSION['user_role']);
    }
    
    /**
     * Check if user has required role
     */
    public static function hasRole($requiredRole) {
        if (!self::isLoggedIn()) {
            return false;
        }
        
        $userRole = $_SESSION['user_role'];
        
        // Role hierarchy: admin > hr > employee
        $roleHierarchy = [
            'admin' => 3,
            'hr' => 2,
            'employee' => 1
        ];
        
        $userLevel = $roleHierarchy[$userRole] ?? 0;
        $requiredLevel = $roleHierarchy[$requiredRole] ?? 0;
        
        return $userLevel >= $requiredLevel;
    }
    
    /**
     * Check session timeout
     */
    public static function checkSessionTimeout() {
        if (!isset($_SESSION['last_activity'])) {
            return false;
        }
        
        $timeout = Config::SESSION_TIMEOUT;
        $lastActivity = $_SESSION['last_activity'];
        
        if (time() - $lastActivity > $timeout) {
            self::$logger->logSecurity('Session timeout', 'User session expired');
            self::logout();
            return false;
        }
        
        $_SESSION['last_activity'] = time();
        return true;
    }
    
    /**
     * Rate limiting for login attempts
     */
    public static function checkLoginAttempts($identifier) {
        $key = 'login_attempts_' . md5($identifier);
        $attempts = $_SESSION[$key] ?? 0;
        $lockoutKey = 'lockout_until_' . md5($identifier);
        $lockoutUntil = $_SESSION[$lockoutKey] ?? 0;
        
        // Check if still locked out
        if ($lockoutUntil > time()) {
            $remainingTime = $lockoutUntil - time();
            self::$logger->logSecurity('Login attempt during lockout', "IP: $identifier, Remaining: {$remainingTime}s");
            return [
                'allowed' => false,
                'remaining_time' => $remainingTime
            ];
        }
        
        // Reset if lockout period has passed
        if ($lockoutUntil > 0 && $lockoutUntil <= time()) {
            unset($_SESSION[$key], $_SESSION[$lockoutKey]);
            $attempts = 0;
        }
        
        return [
            'allowed' => $attempts < Config::MAX_LOGIN_ATTEMPTS,
            'attempts' => $attempts,
            'max_attempts' => Config::MAX_LOGIN_ATTEMPTS
        ];
    }
    
    /**
     * Record failed login attempt
     */
    public static function recordFailedLogin($identifier) {
        $key = 'login_attempts_' . md5($identifier);
        $attempts = ($_SESSION[$key] ?? 0) + 1;
        $_SESSION[$key] = $attempts;
        
        self::$logger->logSecurity('Failed login attempt', "IP: $identifier, Attempts: $attempts");
        
        if ($attempts >= Config::MAX_LOGIN_ATTEMPTS) {
            $lockoutKey = 'lockout_until_' . md5($identifier);
            $_SESSION[$lockoutKey] = time() + Config::LOGIN_LOCKOUT_TIME;
            
            self::$logger->logSecurity('Account locked', "IP: $identifier locked for " . Config::LOGIN_LOCKOUT_TIME . " seconds");
        }
    }
    
    /**
     * Clear login attempts on successful login
     */
    public static function clearLoginAttempts($identifier) {
        $key = 'login_attempts_' . md5($identifier);
        $lockoutKey = 'lockout_until_' . md5($identifier);
        unset($_SESSION[$key], $_SESSION[$lockoutKey]);
    }
    
    /**
     * Logout user
     */
    public static function logout() {
        if (isset($_SESSION['user_id'])) {
            self::$logger->logActivity('User logout', 'User logged out', $_SESSION['user_id']);
        }
        
        session_destroy();
        session_start();
    }
    
    /**
     * Prevent XSS attacks
     */
    public static function escapeOutput($output) {
        return htmlspecialchars($output, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Generate secure filename
     */
    public static function generateSecureFilename($originalName) {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $filename = pathinfo($originalName, PATHINFO_FILENAME);
        
        // Sanitize filename
        $filename = preg_replace('/[^a-zA-Z0-9_-]/', '', $filename);
        $filename = substr($filename, 0, 50); // Limit length
        
        // Add timestamp and random string
        $timestamp = date('YmdHis');
        $random = substr(self::generateToken(), 0, 8);
        
        return $filename . '_' . $timestamp . '_' . $random . '.' . $extension;
    }
    
    /**
     * Validate file upload
     */
    public static function validateFileUpload($file) {
        $errors = [];
        
        // Check if file was uploaded
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $errors[] = 'File upload failed';
            return $errors;
        }
        
        // Check file size
        if ($file['size'] > Config::MAX_FILE_SIZE) {
            $errors[] = 'File size exceeds maximum allowed size';
        }
        
        // Check file type
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, Config::ALLOWED_FILE_TYPES)) {
            $errors[] = 'File type not allowed';
        }
        
        // Check MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $allowedMimes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];
        
        if (!isset($allowedMimes[$extension]) || $allowedMimes[$extension] !== $mimeType) {
            $errors[] = 'Invalid file type';
        }
        
        return $errors;
    }
}
?>
