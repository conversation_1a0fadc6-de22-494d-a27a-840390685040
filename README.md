# Human Resources Center (HRC)

ระบบจัดการทรัพยากรบุคคลแบบครบวงจร พัฒนาด้วย PHP PDO MVC Pattern พร้วมกับ Bootstrap และ SweetAlert2

## คุณสมบัติหลัก

### สำหรับ Admin/HR
- **จัดการข้อมูลพนักงาน** - เพิ่ม แก้ไข ลบ ดูรายละเอียดพนักงาน
- **จัดการเงินเดือน** - บันทึกและจัดการข้อมูลเงินเดือนพนักงาน
- **จัดการการลา** - อนุมัติ/ปฏิเสธคำขอลา ดูประวัติการลา
- **จัดการสวัสดิการ** - กำหนดและจัดการสวัสดิการพนักงาน
- **จัดการประกันสังคม** - ข้อมูลประกันสังคมและสถานพยาบาล
- **รายงานและสstatistics** - Dashboard แสดงสถิติต่างๆ
- **จัดการผู้ใช้** - สร้างและจัดการบัญชีผู้ใช้
- **ระบบ Logs** - ติดตามการใช้งานระบบ

### สำหรับพนักงาน (Employee)
- **ดูข้อมูลส่วนตัว** - ข้อมูลพนักงาน ประวัติการทำงาน
- **ขอลา** - ส่งคำขอลาและติดตามสถานะ
- **ดูสลิปเงินเดือน** - ดาวน์โหลดสลิปเงินเดือน (เข้ารหัส)
- **ดูสวัสดิการ** - ข้อมูลสวัสดิการที่มีสิทธิ์
- **อัพเดทข้อมูล** - แก้ไขข้อมูลส่วนตัวบางส่วน

## ความปลอดภัย

- **Authentication & Authorization** - ระบบล็อกอินและการแยกสิทธิ์การเข้าถึง
- **Password Hashing** - เข้ารหัสรหัสผ่านด้วย PHP password_hash()
- **CSRF Protection** - ป้องกันการโจมตี CSRF
- **Input Validation** - ตรวจสอบข้อมูลก่อนบันทึก
- **SQL Injection Prevention** - ใช้ Prepared Statements
- **Session Management** - จัดการ Session อย่างปลอดภัย
- **Rate Limiting** - จำกัดการเข้าถึงเพื่อป้องกันการโจมตี
- **File Upload Security** - ตรวจสอบไฟล์ที่อัพโหลด

## ระบบ Logging

- **Access Logs** - บันทึกการเข้าถึงระบบ
- **Activity Logs** - บันทึกกิจกรรมของผู้ใช้
- **Error Logs** - บันทึกข้อผิดพลาด
- **Security Logs** - บันทึกเหตุการณ์ด้านความปลอดภัย
- **Log Archiving** - เก็บ logs เก่าอัตโนมัติ

## เทคโนโลยีที่ใช้

- **Backend**: PHP 7.4+, PDO, MySQL
- **Frontend**: Bootstrap 5.3, jQ<PERSON>y, SweetAlert2
- **Architecture**: MVC Pattern
- **Security**: CSRF Protection, Password Hashing, Input Validation
- **Charts**: Chart.js
- **Icons**: Bootstrap Icons

## การติดตั้ง

### ความต้องการของระบบ
- PHP 7.4 หรือสูงกว่า
- MySQL 5.7 หรือสูงกว่า
- Apache/Nginx Web Server
- mod_rewrite enabled

### ขั้นตอนการติดตั้ง

1. **Clone หรือดาวน์โหลดโปรเจค**
```bash
git clone https://github.com/your-repo/hrc.git
cd hrc
```

2. **สร้างฐานข้อมูล**
```sql
-- เข้า MySQL และรันคำสั่ง
source Config/database_schema.sql
```

3. **กำหนดค่า Database**
แก้ไขไฟล์ `Config/Database.php`:
```php
private $host = 'localhost';
private $dbname = 'hrc_database';
private $username = 'your_username';
private $password = 'your_password';
```

4. **กำหนดค่าระบบ**
แก้ไขไฟล์ `Config/Config.php`:
```php
const APP_URL = 'http://your-domain.com/hrc';
const ENCRYPTION_KEY = 'your-secret-key-here';
```

5. **ตั้งค่า Permissions**
```bash
chmod 755 Assets/uploads/
chmod 755 Logs/
```

6. **เข้าถึงระบบ**
เปิดเบราว์เซอร์และไปที่ `http://your-domain.com/hrc`

### บัญชีเริ่มต้น
- **Admin**: username: `admin`, password: `admin123`

## โครงสร้างโปรเจค

```
hrc/
├── Assets/
│   ├── css/
│   ├── js/
│   ├── images/
│   └── uploads/
├── Config/
│   ├── Config.php
│   ├── Database.php
│   └── database_schema.sql
├── Controllers/
│   ├── BaseController.php
│   ├── AuthController.php
│   ├── AdminController.php
│   ├── EmployeeController.php
│   └── HomeController.php
├── Helpers/
│   ├── Logger.php
│   ├── Security.php
│   └── Validator.php
├── Logs/
├── Middleware/
│   └── AuthMiddleware.php
├── Models/
│   ├── BaseModel.php
│   ├── User.php
│   ├── Employee.php
│   ├── SalaryRecord.php
│   └── LeaveRequest.php
├── Views/
│   ├── layouts/
│   ├── admin/
│   ├── user/
│   ├── auth/
│   └── errors/
├── .htaccess
├── index.php
└── README.md
```

## การใช้งาน

### Admin/HR
1. เข้าสู่ระบบด้วยบัญชี Admin
2. ไปที่ Admin Dashboard
3. จัดการข้อมูลพนักงาน เงินเดือน การลา ฯลฯ

### พนักงาน
1. เข้าสู่ระบบด้วยบัญชีพนักงาน
2. ดูข้อมูลส่วนตัวและสลิปเงินเดือน
3. ส่งคำขอลาผ่านระบบ

## การพัฒนาต่อ

### เพิ่มฟีเจอร์ใหม่
1. สร้าง Model ใน `Models/`
2. สร้าง Controller ใน `Controllers/`
3. สร้าง View ใน `Views/`
4. เพิ่ม Route ใน `index.php` (ถ้าจำเป็น)

### การ Debug
- ดู Error Logs ใน `Logs/error/`
- เปิด Debug Mode ใน `Config/Config.php`
- ใช้ Browser Developer Tools

## การสนับสนุน

หากพบปัญหาหรือต้องการความช่วยเหลือ:
1. ตรวจสอบ Error Logs
2. ดู Documentation
3. ติดต่อทีมพัฒนา

## License

MIT License - ดูรายละเอียดใน LICENSE file

## การมีส่วนร่วม

ยินดีรับ Pull Requests และ Issues
1. Fork โปรเจค
2. สร้าง Feature Branch
3. Commit การเปลี่ยนแปลง
4. Push ไปยัง Branch
5. สร้าง Pull Request

---

**Human Resources Center** - ระบบจัดการทรัพยากรบุคคลที่ครบครันและปลอดภัย
