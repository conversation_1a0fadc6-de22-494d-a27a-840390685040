<?php
/**
 * Human Resources Center - Main Entry Point
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

// Start session
session_start();

// Set timezone
date_default_timezone_set('Asia/Bangkok');

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define constants
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/Config');
define('CONTROLLERS_PATH', ROOT_PATH . '/Controllers');
define('MODELS_PATH', ROOT_PATH . '/Models');
define('VIEWS_PATH', ROOT_PATH . '/Views');
define('HELPERS_PATH', ROOT_PATH . '/Helpers');
define('MIDDLEWARE_PATH', ROOT_PATH . '/Middleware');
define('LOGS_PATH', ROOT_PATH . '/Logs');
define('ASSETS_PATH', ROOT_PATH . '/Assets');

// Autoloader
spl_autoload_register(function ($class) {
    $paths = [
        CONFIG_PATH,
        CONTROLLERS_PATH,
        MODELS_PATH,
        HELPERS_PATH,
        MIDDLEWARE_PATH
    ];
    
    foreach ($paths as $path) {
        $file = $path . '/' . $class . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// Load configuration
require_once CONFIG_PATH . '/Database.php';
require_once CONFIG_PATH . '/Config.php';

// Load helpers
require_once HELPERS_PATH . '/Logger.php';
require_once HELPERS_PATH . '/Security.php';
require_once HELPERS_PATH . '/Validator.php';

// Initialize logger
$logger = new Logger();

try {
    // Get URL from query string
    $url = isset($_GET['url']) ? $_GET['url'] : '';
    $url = rtrim($url, '/');
    $url = filter_var($url, FILTER_SANITIZE_URL);
    
    // Parse URL
    $urlArray = explode('/', $url);
    
    // Default routing
    $controllerName = !empty($urlArray[0]) ? ucfirst($urlArray[0]) . 'Controller' : 'HomeController';
    $methodName = !empty($urlArray[1]) ? $urlArray[1] : 'index';
    $params = array_slice($urlArray, 2);
    
    // Log access
    $logger->logAccess($_SERVER['REQUEST_METHOD'], $url, $_SERVER['REMOTE_ADDR']);
    
    // Check if controller exists
    $controllerFile = CONTROLLERS_PATH . '/' . $controllerName . '.php';
    if (!file_exists($controllerFile)) {
        throw new Exception("Controller not found: $controllerName");
    }
    
    // Load and instantiate controller
    require_once $controllerFile;
    
    if (!class_exists($controllerName)) {
        throw new Exception("Controller class not found: $controllerName");
    }
    
    $controller = new $controllerName();
    
    // Check if method exists
    if (!method_exists($controller, $methodName)) {
        throw new Exception("Method not found: $methodName in $controllerName");
    }
    
    // Call controller method
    call_user_func_array([$controller, $methodName], $params);
    
} catch (Exception $e) {
    // Log error
    $logger->logError($e->getMessage(), $e->getTraceAsString());
    
    // Show error page
    http_response_code(404);
    include VIEWS_PATH . '/errors/404.php';
}
?>
