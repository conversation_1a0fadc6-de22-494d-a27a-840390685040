-- Sample Data for Human Resources Center
-- Run this after database_schema.sql

USE hrc_database;

-- Insert sample users
INSERT INTO users (username, email, password_hash, role, is_active) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1),
('hr_manager', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'hr', 1),
('john.doe', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'employee', 1),
('jane.smith', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'employee', 1),
('mike.wilson', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'employee', 1),
('sarah.johnson', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'employee', 1);

-- Insert sample employees
INSERT INTO employees (user_id, employee_code, first_name, last_name, email, phone, address, birth_date, hire_date, position, department, manager_id, salary, status) VALUES
(2, 'HR001', 'สมชาย', 'ใจดี', '<EMAIL>', '02-123-4567', '123 ถนนสุขุมวิท กรุงเทพฯ 10110', '1985-03-15', '2020-01-15', 'HR Manager', 'Human Resources', NULL, 50000.00, 'active'),
(3, 'EMP001', 'จอห์น', 'โด', '<EMAIL>', '08-1234-5678', '456 ถนนพหลโยธิน กรุงเทพฯ 10400', '1990-07-20', '2021-03-01', 'Software Developer', 'IT', 2, 45000.00, 'active'),
(4, 'EMP002', 'เจน', 'สมิธ', '<EMAIL>', '08-2345-6789', '789 ถนนรัชดาภิเษก กรุงเทพฯ 10310', '1988-11-12', '2020-08-15', 'Marketing Specialist', 'Marketing', 2, 40000.00, 'active'),
(5, 'EMP003', 'ไมค์', 'วิลสัน', '<EMAIL>', '08-3456-7890', '321 ถนนสีลม กรุงเทพฯ 10500', '1992-05-08', '2022-01-10', 'Sales Executive', 'Sales', 2, 38000.00, 'active'),
(6, 'EMP004', 'ซาร่า', 'จอห์นสัน', '<EMAIL>', '08-4567-8901', '654 ถนนเพชรบุรี กรุงเทพฯ 10400', '1987-09-25', '2021-06-01', 'Accountant', 'Finance', 2, 42000.00, 'active');

-- Insert sample salary records
INSERT INTO salary_records (employee_id, base_salary, allowances, overtime_pay, deductions, net_salary, pay_period_start, pay_period_end, pay_date, status, created_by) VALUES
-- HR Manager
(1, 50000.00, 5000.00, 0.00, 3000.00, 52000.00, '2024-07-01', '2024-07-31', '2024-08-01', 'paid', 1),
(1, 50000.00, 5000.00, 0.00, 3000.00, 52000.00, '2024-06-01', '2024-06-30', '2024-07-01', 'paid', 1),

-- John Doe
(2, 45000.00, 3000.00, 2000.00, 2500.00, 47500.00, '2024-07-01', '2024-07-31', '2024-08-01', 'paid', 1),
(2, 45000.00, 3000.00, 1500.00, 2500.00, 47000.00, '2024-06-01', '2024-06-30', '2024-07-01', 'paid', 1),

-- Jane Smith
(3, 40000.00, 2500.00, 1000.00, 2200.00, 41300.00, '2024-07-01', '2024-07-31', '2024-08-01', 'paid', 1),
(3, 40000.00, 2500.00, 800.00, 2200.00, 41100.00, '2024-06-01', '2024-06-30', '2024-07-01', 'paid', 1),

-- Mike Wilson
(4, 38000.00, 2000.00, 1500.00, 2100.00, 39400.00, '2024-07-01', '2024-07-31', '2024-08-01', 'paid', 1),
(4, 38000.00, 2000.00, 1200.00, 2100.00, 39100.00, '2024-06-01', '2024-06-30', '2024-07-01', 'paid', 1),

-- Sarah Johnson
(5, 42000.00, 2800.00, 500.00, 2300.00, 43000.00, '2024-07-01', '2024-07-31', '2024-08-01', 'paid', 1),
(5, 42000.00, 2800.00, 300.00, 2300.00, 42800.00, '2024-06-01', '2024-06-30', '2024-07-01', 'paid', 1);

-- Insert sample leave requests
INSERT INTO leave_requests (employee_id, leave_type_id, start_date, end_date, days_requested, reason, status, approved_by, approved_at) VALUES
(2, 1, '2024-08-15', '2024-08-16', 2, 'Personal vacation', 'approved', 2, '2024-08-01 10:30:00'),
(3, 2, '2024-08-10', '2024-08-10', 1, 'Medical appointment', 'approved', 2, '2024-08-05 14:15:00'),
(4, 1, '2024-08-20', '2024-08-23', 4, 'Family trip', 'pending', NULL, NULL),
(5, 3, '2024-08-12', '2024-08-12', 1, 'Personal matters', 'approved', 2, '2024-08-08 09:45:00'),
(2, 2, '2024-07-25', '2024-07-25', 1, 'Feeling unwell', 'approved', 2, '2024-07-24 16:20:00');

-- Insert sample attendance records
INSERT INTO attendance (employee_id, date, check_in, check_out, total_hours, status) VALUES
-- Recent attendance for all employees
(1, '2024-08-07', '08:30:00', '17:30:00', 8.00, 'present'),
(2, '2024-08-07', '09:00:00', '18:00:00', 8.00, 'present'),
(3, '2024-08-07', '08:45:00', '17:45:00', 8.00, 'present'),
(4, '2024-08-07', '09:15:00', '18:15:00', 8.00, 'late'),
(5, '2024-08-07', '08:30:00', '17:30:00', 8.00, 'present'),

(1, '2024-08-06', '08:30:00', '17:30:00', 8.00, 'present'),
(2, '2024-08-06', '09:00:00', '18:00:00', 8.00, 'present'),
(3, '2024-08-06', '08:45:00', '17:45:00', 8.00, 'present'),
(4, '2024-08-06', '09:00:00', '18:00:00', 8.00, 'present'),
(5, '2024-08-06', '08:30:00', '17:30:00', 8.00, 'present');

-- Insert sample employee benefits
INSERT INTO employee_benefits (employee_id, benefit_id, start_date, amount, status) VALUES
(1, 1, '2020-01-15', 5000.00, 'active'),
(1, 2, '2020-01-15', 2000.00, 'active'),
(1, 3, '2020-01-15', 1500.00, 'active'),
(1, 4, '2020-01-15', 200.00, 'active'),

(2, 1, '2021-03-01', 5000.00, 'active'),
(2, 2, '2021-03-01', 2000.00, 'active'),
(2, 3, '2021-03-01', 1500.00, 'active'),
(2, 4, '2021-03-01', 200.00, 'active'),

(3, 1, '2020-08-15', 5000.00, 'active'),
(3, 3, '2020-08-15', 1500.00, 'active'),
(3, 4, '2020-08-15', 200.00, 'active'),

(4, 1, '2022-01-10', 5000.00, 'active'),
(4, 3, '2022-01-10', 1500.00, 'active'),
(4, 4, '2022-01-10', 200.00, 'active'),

(5, 1, '2021-06-01', 5000.00, 'active'),
(5, 2, '2021-06-01', 2000.00, 'active'),
(5, 3, '2021-06-01', 1500.00, 'active'),
(5, 4, '2021-06-01', 200.00, 'active');

-- Insert sample social security records
INSERT INTO social_security (employee_id, ss_number, hospital_name, hospital_code, start_date, monthly_contribution, status) VALUES
(1, '1234567890123', 'โรงพยาบาลจุฬาลงกรณ์', 'CU001', '2020-01-15', 750.00, 'active'),
(2, '2345678901234', 'โรงพยาบาลศิริราช', 'SI001', '2021-03-01', 675.00, 'active'),
(3, 'ิ3456789012345', 'โรงพยาบาลรามาธิบดี', 'RA001', '2020-08-15', 600.00, 'active'),
(4, '4567890123456', 'โรงพยาบาลเกษมราษฎร์', 'KR001', '2022-01-10', 570.00, 'active'),
(5, '5678901234567', 'โรงพยาบาลบำรุงราษฎร์', 'BR001', '2021-06-01', 630.00, 'active');

-- Insert some system logs for demonstration
INSERT INTO system_logs (log_type, level, message, user_id, ip_address, user_agent, request_method, request_uri) VALUES
('access', 'INFO', 'Access: GET /', 1, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'GET', '/'),
('activity', 'INFO', 'Activity: User login - User: admin', 1, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'POST', '/auth/login'),
('access', 'INFO', 'Access: GET /admin/dashboard', 1, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'GET', '/admin/dashboard'),
('activity', 'INFO', 'Activity: Employee created - Employee: จอห์น โด (EMP001)', 1, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'POST', '/admin/employees'),
('security', 'INFO', 'Security: Successful login - User: admin, IP: 127.0.0.1', 1, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'POST', '/auth/login');

-- Update employee manager relationships
UPDATE employees SET manager_id = 1 WHERE id IN (2, 3, 4, 5);

COMMIT;
