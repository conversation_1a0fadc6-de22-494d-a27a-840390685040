<?php
/**
 * Application Configuration
 */

class Config {
    // Application settings
    const APP_NAME = 'Human Resources Center';
    const APP_VERSION = '1.0.0';
    const APP_URL = 'http://localhost/hrc';
    
    // Security settings
    const ENCRYPTION_KEY = 'your-secret-encryption-key-here-change-this';
    const SESSION_TIMEOUT = 3600; // 1 hour
    const MAX_LOGIN_ATTEMPTS = 5;
    const LOGIN_LOCKOUT_TIME = 900; // 15 minutes
    
    // Password settings
    const PASSWORD_MIN_LENGTH = 8;
    const PASSWORD_REQUIRE_UPPERCASE = true;
    const PASSWORD_REQUIRE_LOWERCASE = true;
    const PASSWORD_REQUIRE_NUMBERS = true;
    const PASSWORD_REQUIRE_SYMBOLS = true;
    
    // File upload settings
    const MAX_FILE_SIZE = 5242880; // 5MB
    const ALLOWED_FILE_TYPES = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'];
    const UPLOAD_PATH = 'Assets/uploads/';
    
    // Logging settings
    const LOG_LEVEL = 'INFO'; // DEBUG, INFO, WARNING, ERROR
    const LOG_MAX_SIZE = 10485760; // 10MB
    const LOG_ARCHIVE_DAYS = 30;
    
    // Email settings (for notifications)
    const SMTP_HOST = 'smtp.gmail.com';
    const SMTP_PORT = 587;
    const SMTP_USERNAME = '';
    const SMTP_PASSWORD = '';
    const SMTP_FROM_EMAIL = '';
    const SMTP_FROM_NAME = 'HR Center';
    
    // LINE LIFF settings
    const LINE_CHANNEL_ID = '';
    const LINE_CHANNEL_SECRET = '';
    const LINE_LIFF_ID = '';
    
    // Pagination
    const ITEMS_PER_PAGE = 20;
    
    // Date format
    const DATE_FORMAT = 'Y-m-d';
    const DATETIME_FORMAT = 'Y-m-d H:i:s';
    const DISPLAY_DATE_FORMAT = 'd/m/Y';
    const DISPLAY_DATETIME_FORMAT = 'd/m/Y H:i:s';
    
    /**
     * Get configuration value
     */
    public static function get($key, $default = null) {
        return defined("self::$key") ? constant("self::$key") : $default;
    }
    
    /**
     * Check if running in development mode
     */
    public static function isDevelopment() {
        return $_SERVER['HTTP_HOST'] === 'localhost' || 
               strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false ||
               strpos($_SERVER['HTTP_HOST'], '.local') !== false;
    }
    
    /**
     * Get base URL
     */
    public static function getBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $path = dirname($_SERVER['SCRIPT_NAME']);
        return $protocol . '://' . $host . $path;
    }
}
?>
