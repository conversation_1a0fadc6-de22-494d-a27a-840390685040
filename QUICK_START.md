# 🚀 HRC Quick Start Guide

## ปัญหา "Not Found" และวิธีแก้ไข

### วิธีที่ 1: ใช้ Setup Wizard (แนะนำ)
1. เปิดเบราว์เซอร์ไปที่: `http://localhost/hrc/setup.php`
2. ทำตามขั้นตอนการติดตั้ง 4 ขั้นตอน
3. ระบบจะตรวจสอบและแก้ไขปัญหาอัตโนมัติ

### วิธีที่ 2: ตรวจสอบด้วยตนเอง
1. เปิดเบราว์เซอร์ไปที่: `http://localhost/hrc/test.php`
2. ดูผลการตรวจสอบระบบ
3. แก้ไขปัญหาตามที่แสดง

### วิธีที่ 3: เข้าใช้งานโดยตรง
หากยังมีปัญหา ให้ใช้ URL แบบเต็ม:
- **หน้าแรก**: `http://localhost/hrc/index.php`
- **เข้าสู่ระบบ**: `http://localhost/hrc/index.php?url=auth/login`
- **Admin**: `http://localhost/hrc/index.php?url=admin/dashboard`

## การแก้ไขปัญหาทั่วไป

### 1. mod_rewrite ไม่ทำงาน
**สำหรับ XAMPP/WAMP:**
```apache
# ใน httpd.conf หรือ apache2.conf
LoadModule rewrite_module modules/mod_rewrite.so

# ใน Virtual Host หรือ Directory
<Directory "C:/xampp/htdocs/hrc">
    AllowOverride All
    Require all granted
</Directory>
```

**สำหรับ Laragon:**
- mod_rewrite เปิดอยู่แล้วโดยปกติ
- ตรวจสอบที่ Menu > Apache > Version

### 2. ฐานข้อมูลไม่เชื่อมต่อ
```sql
-- สร้างฐานข้อมูลใน phpMyAdmin หรือ MySQL
CREATE DATABASE hrc_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Import ไฟล์ SQL
source Config/database_schema.sql;
source Config/sample_data.sql;
```

### 3. Permission ไม่ถูกต้อง
```bash
# Linux/Mac
chmod 755 Assets/uploads/
chmod 755 Logs/
chmod 644 .htaccess

# Windows - ตั้งค่าใน Properties > Security
```

## บัญชีเริ่มต้น

| Role | Username | Password | Email |
|------|----------|----------|-------|
| Admin | admin | admin123 | <EMAIL> |
| HR Manager | hr_manager | admin123 | <EMAIL> |
| Employee | john.doe | admin123 | <EMAIL> |

## URL Patterns

### ✅ URL ที่ใช้งานได้ (หลัง mod_rewrite ทำงาน)
- `/` - หน้าแรก
- `/auth/login` - เข้าสู่ระบบ
- `/admin/dashboard` - Admin Dashboard
- `/admin/employees` - จัดการพนักงาน
- `/dashboard` - Employee Dashboard

### 🔄 URL สำรอง (ถ้า mod_rewrite ไม่ทำงาน)
- `/index.php` - หน้าแรก
- `/index.php?url=auth/login` - เข้าสู่ระบบ
- `/index.php?url=admin/dashboard` - Admin Dashboard
- `/index.php?url=admin/employees` - จัดการพนักงาน

## การตรวจสอบระบบ

### 1. ตรวจสอบ Apache Modules
```php
// ใน test.php
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    var_dump(in_array('mod_rewrite', $modules));
}
```

### 2. ตรวจสอบ PHP Extensions
```php
$required = ['pdo', 'pdo_mysql', 'openssl', 'json', 'mbstring'];
foreach ($required as $ext) {
    echo $ext . ': ' . (extension_loaded($ext) ? 'OK' : 'Missing') . "\n";
}
```

### 3. ตรวจสอบ Database
```php
try {
    $pdo = new PDO('mysql:host=localhost;dbname=hrc_database', 'root', '');
    echo "Database: Connected\n";
} catch (Exception $e) {
    echo "Database: " . $e->getMessage() . "\n";
}
```

## การแก้ไขเฉพาะ Laragon

### 1. เปิด mod_rewrite
1. คลิกขวาที่ Laragon tray icon
2. เลือก Apache > Version > เลือก version ที่ใช้
3. หรือไปที่ Menu > Apache > httpd.conf
4. ตรวจสอบว่ามี `LoadModule rewrite_module modules/mod_rewrite.so`

### 2. ตั้งค่า Virtual Host
```apache
<VirtualHost *:80>
    DocumentRoot "C:/laragon/www/hrc"
    ServerName hrc.test
    <Directory "C:/laragon/www/hrc">
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

### 3. เพิ่มใน hosts file
```
127.0.0.1 hrc.test
```

## การใช้งานหลังติดตั้ง

### 1. เข้าสู่ระบบ Admin
1. ไปที่ `/auth/login`
2. ใส่ username: `admin`, password: `admin123`
3. จะถูก redirect ไปที่ Admin Dashboard

### 2. จัดการพนักงาน
1. ไปที่ Admin > Employees
2. คลิก "Add Employee" เพื่อเพิ่มพนักงานใหม่
3. กรอกข้อมูลและบันทึก

### 3. จัดการเงินเดือน
1. ไปที่ Admin > Salaries
2. เลือกพนักงานและเพิ่มข้อมูลเงินเดือน

## การรักษาความปลอดภัย

### 1. เปลี่ยนรหัสผ่านเริ่มต้น
```sql
UPDATE users SET password_hash = '$2y$10$newhashedpassword' WHERE username = 'admin';
```

### 2. อัพเดท Encryption Key
```php
// ใน Config/Config.php
const ENCRYPTION_KEY = 'your-new-secret-key-32-characters-long';
```

### 3. ลบไฟล์ที่ไม่จำเป็น
```bash
rm setup.php test.php
```

## การแก้ไขปัญหาเพิ่มเติม

### ถ้ายังไม่ได้ ลองวิธีนี้:
1. ตรวจสอบ error log ของ Apache
2. เปิด PHP error reporting
3. ตรวจสอบ file permissions
4. ลองใช้ URL แบบเต็ม `index.php?url=...`

### ติดต่อสนับสนุน:
- ตรวจสอบ error logs ใน `Logs/error/`
- ดู Apache error log
- ใช้ browser developer tools

---

**หมายเหตุ**: หากยังมีปัญหา ให้รัน `test.php` เพื่อดูรายละเอียดการตั้งค่าระบบ
