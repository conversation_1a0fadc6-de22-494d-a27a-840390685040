/**
 * Admin Panel JavaScript
 * Common functionality for admin interface
 */

$(document).ready(function() {
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Auto-hide alerts
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
    
    // Confirm delete actions
    $(document).on('click', '.btn-delete, .delete-btn', function(e) {
        e.preventDefault();
        
        const element = $(this);
        const url = element.attr('href') || element.data('url');
        const title = element.data('title') || 'Are you sure?';
        const text = element.data('text') || 'This action cannot be undone.';
        const confirmText = element.data('confirm') || 'Yes, delete it!';
        
        Swal.fire({
            title: title,
            text: text,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: confirmText,
            cancelButtonText: 'Cancel',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                if (element.is('form') || element.closest('form').length) {
                    element.closest('form').submit();
                } else if (element.data('method') === 'POST') {
                    // Create and submit a form for POST requests
                    const form = $('<form>', {
                        method: 'POST',
                        action: url
                    });
                    
                    // Add CSRF token
                    form.append($('<input>', {
                        type: 'hidden',
                        name: 'csrf_token',
                        value: $('meta[name="csrf-token"]').attr('content') || window.csrfToken
                    }));
                    
                    $('body').append(form);
                    form.submit();
                } else {
                    window.location.href = url;
                }
            }
        });
    });
    
    // Active navigation highlighting
    const currentPath = window.location.pathname;
    $('.sidebar .nav-link').each(function() {
        const href = $(this).attr('href');
        if (currentPath.startsWith(href) && href !== '/') {
            $(this).addClass('active');
        }
    });
    
    // Form validation
    $('form[data-validate="true"]').each(function() {
        $(this).on('submit', function(e) {
            const form = $(this);
            let isValid = true;
            
            // Clear previous errors
            form.find('.is-invalid').removeClass('is-invalid');
            form.find('.invalid-feedback').remove();
            
            // Validate required fields
            form.find('[required]').each(function() {
                const field = $(this);
                if (!field.val().trim()) {
                    field.addClass('is-invalid');
                    field.after('<div class="invalid-feedback">This field is required.</div>');
                    isValid = false;
                }
            });
            
            // Validate email fields
            form.find('input[type="email"]').each(function() {
                const field = $(this);
                const email = field.val().trim();
                if (email && !isValidEmail(email)) {
                    field.addClass('is-invalid');
                    field.after('<div class="invalid-feedback">Please enter a valid email address.</div>');
                    isValid = false;
                }
            });
            
            // Validate password confirmation
            const password = form.find('input[name="password"]').val();
            const confirmPassword = form.find('input[name="confirm_password"]').val();
            if (password && confirmPassword && password !== confirmPassword) {
                form.find('input[name="confirm_password"]').addClass('is-invalid');
                form.find('input[name="confirm_password"]').after('<div class="invalid-feedback">Password confirmation does not match.</div>');
                isValid = false;
            }
            
            if (!isValid) {
                e.preventDefault();
                // Scroll to first error
                const firstError = form.find('.is-invalid').first();
                if (firstError.length) {
                    $('html, body').animate({
                        scrollTop: firstError.offset().top - 100
                    }, 500);
                }
            }
        });
    });
    
    // AJAX form submission
    $(document).on('submit', 'form[data-ajax="true"]', function(e) {
        e.preventDefault();
        
        const form = $(this);
        const submitBtn = form.find('button[type="submit"]');
        const originalText = submitBtn.html();
        const loadingText = submitBtn.data('loading') || 'Processing...';
        
        // Show loading state
        submitBtn.html('<i class="bi bi-hourglass-split me-2"></i>' + loadingText).prop('disabled', true);
        
        // Prepare form data
        const formData = new FormData(this);
        
        $.ajax({
            url: form.attr('action') || window.location.pathname,
            method: form.attr('method') || 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: response.message,
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        if (response.redirect) {
                            window.location.href = response.redirect;
                        } else {
                            location.reload();
                        }
                    });
                } else {
                    handleFormErrors(form, response);
                }
            },
            error: function(xhr) {
                let message = 'An error occurred. Please try again.';
                
                if (xhr.responseJSON) {
                    if (xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    
                    if (xhr.responseJSON.errors) {
                        handleFormErrors(form, xhr.responseJSON);
                        return;
                    }
                }
                
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: message
                });
            },
            complete: function() {
                // Restore button state
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });
    
    // Data tables initialization
    if ($.fn.DataTable) {
        $('.data-table').DataTable({
            responsive: true,
            pageLength: 25,
            order: [[0, 'desc']],
            language: {
                search: "Search:",
                lengthMenu: "Show _MENU_ entries",
                info: "Showing _START_ to _END_ of _TOTAL_ entries",
                infoEmpty: "No entries found",
                infoFiltered: "(filtered from _MAX_ total entries)",
                paginate: {
                    first: "First",
                    last: "Last",
                    next: "Next",
                    previous: "Previous"
                }
            }
        });
    }
    
    // File upload preview
    $(document).on('change', 'input[type="file"][data-preview]', function() {
        const input = this;
        const previewContainer = $(input.data('preview'));
        
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const fileType = input.files[0].type;
                
                if (fileType.startsWith('image/')) {
                    previewContainer.html('<img src="' + e.target.result + '" class="img-thumbnail" style="max-width: 200px;">');
                } else {
                    previewContainer.html('<div class="alert alert-info"><i class="bi bi-file-earmark me-2"></i>' + input.files[0].name + '</div>');
                }
            };
            
            reader.readAsDataURL(input.files[0]);
        }
    });
    
    // Auto-save form data
    $('form[data-autosave="true"]').each(function() {
        const form = $(this);
        const formId = form.attr('id') || 'autosave-form';
        
        // Load saved data
        const savedData = localStorage.getItem('autosave-' + formId);
        if (savedData) {
            try {
                const data = JSON.parse(savedData);
                Object.keys(data).forEach(function(key) {
                    const field = form.find('[name="' + key + '"]');
                    if (field.length && field.attr('type') !== 'password') {
                        field.val(data[key]);
                    }
                });
            } catch (e) {
                console.error('Error loading autosave data:', e);
            }
        }
        
        // Save data on change
        form.on('change input', 'input, select, textarea', function() {
            const formData = {};
            form.find('input, select, textarea').each(function() {
                const field = $(this);
                if (field.attr('name') && field.attr('type') !== 'password') {
                    formData[field.attr('name')] = field.val();
                }
            });
            
            localStorage.setItem('autosave-' + formId, JSON.stringify(formData));
        });
        
        // Clear saved data on successful submit
        form.on('submit', function() {
            localStorage.removeItem('autosave-' + formId);
        });
    });
    
    // Mobile sidebar toggle
    $('.sidebar-toggle').on('click', function() {
        $('.sidebar').toggleClass('show');
    });
    
    // Close sidebar when clicking outside on mobile
    $(document).on('click', function(e) {
        if ($(window).width() <= 768) {
            if (!$(e.target).closest('.sidebar, .sidebar-toggle').length) {
                $('.sidebar').removeClass('show');
            }
        }
    });
    
});

// Utility functions
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function handleFormErrors(form, response) {
    // Clear previous errors
    form.find('.is-invalid').removeClass('is-invalid');
    form.find('.invalid-feedback').remove();
    
    if (response.errors) {
        let firstError = null;
        
        Object.keys(response.errors).forEach(function(fieldName) {
            const field = form.find('[name="' + fieldName + '"]');
            if (field.length) {
                field.addClass('is-invalid');
                
                const errors = Array.isArray(response.errors[fieldName]) 
                    ? response.errors[fieldName] 
                    : [response.errors[fieldName]];
                
                errors.forEach(function(error) {
                    field.after('<div class="invalid-feedback">' + error + '</div>');
                });
                
                if (!firstError) {
                    firstError = field;
                }
            }
        });
        
        // Scroll to first error
        if (firstError) {
            $('html, body').animate({
                scrollTop: firstError.offset().top - 100
            }, 500);
        }
    }
    
    // Show general error message
    if (response.message) {
        Swal.fire({
            icon: 'error',
            title: 'Validation Error',
            text: response.message
        });
    }
}

function showLoading(message = 'Loading...') {
    Swal.fire({
        title: message,
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
}

function hideLoading() {
    Swal.close();
}

function showSuccess(message, callback = null) {
    Swal.fire({
        icon: 'success',
        title: 'Success!',
        text: message,
        timer: 2000,
        showConfirmButton: false
    }).then(() => {
        if (callback) callback();
    });
}

function showError(message) {
    Swal.fire({
        icon: 'error',
        title: 'Error',
        text: message
    });
}

function confirmAction(title, text, callback) {
    Swal.fire({
        title: title,
        text: text,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed && callback) {
            callback();
        }
    });
}

// Export functions for global use
window.HRC = {
    showLoading,
    hideLoading,
    showSuccess,
    showError,
    confirmAction,
    isValidEmail,
    handleFormErrors
};
