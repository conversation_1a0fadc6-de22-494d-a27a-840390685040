<?php
/**
 * Admin Controller
 * Handles admin dashboard and main admin functions
 */

require_once 'BaseController.php';

class AdminController extends BaseController {
    
    public function __construct() {
        parent::__construct();
        
        // Require HR or Admin role
        if (!$this->requireHR()) {
            return;
        }
    }
    
    /**
     * Admin Dashboard
     */
    public function dashboard() {
        try {
            // Load models
            $employeeModel = new Employee();
            $salaryModel = new SalaryRecord();
            $leaveModel = new LeaveRequest();
            
            // Get statistics
            $employeeStats = $employeeModel->getStatistics();
            $salaryStats = $salaryModel->getStatistics();
            $leaveStats = $leaveModel->getStatistics();
            
            // Get recent activities
            $recentEmployees = $employeeModel->getAllWithUsers(['e.status' => 'active'], 'e.created_at DESC', 5);
            $pendingLeaves = $leaveModel->getPending();
            $upcomingBirthdays = $employeeModel->getUpcomingBirthdays(30);
            $workAnniversaries = $employeeModel->getWorkAnniversaries(30);
            
            // Get current month salary records
            $currentMonthSalaries = $salaryModel->getCurrentMonth();
            
            $this->data['page_title'] = 'Admin Dashboard';
            $this->data['employee_stats'] = $employeeStats;
            $this->data['salary_stats'] = $salaryStats;
            $this->data['leave_stats'] = $leaveStats;
            $this->data['recent_employees'] = $recentEmployees;
            $this->data['pending_leaves'] = $pendingLeaves;
            $this->data['upcoming_birthdays'] = $upcomingBirthdays;
            $this->data['work_anniversaries'] = $workAnniversaries;
            $this->data['current_month_salaries'] = $currentMonthSalaries;
            $this->data['flash'] = $this->getFlash();
            
            $this->view('admin/dashboard');
            
        } catch (Exception $e) {
            $this->logger->logError('Admin dashboard error: ' . $e->getMessage());
            $this->setFlash('An error occurred while loading the dashboard', 'error');
            $this->view('admin/dashboard');
        }
    }
    
    /**
     * System Settings
     */
    public function settings() {
        if (!$this->requireAdmin()) {
            return;
        }
        
        $this->data['page_title'] = 'System Settings';
        $this->data['flash'] = $this->getFlash();
        
        $this->view('admin/settings');
    }
    
    /**
     * System Logs
     */
    public function logs() {
        if (!$this->requireAdmin()) {
            return;
        }
        
        try {
            $page = (int)($_GET['page'] ?? 1);
            $type = $_GET['type'] ?? null;
            $level = $_GET['level'] ?? null;
            
            $logs = $this->logger->getLogs($type, $level, 50, ($page - 1) * 50);
            
            $this->data['page_title'] = 'System Logs';
            $this->data['logs'] = $logs;
            $this->data['current_page'] = $page;
            $this->data['filter_type'] = $type;
            $this->data['filter_level'] = $level;
            $this->data['flash'] = $this->getFlash();
            
            $this->view('admin/logs');
            
        } catch (Exception $e) {
            $this->logger->logError('Logs view error: ' . $e->getMessage());
            $this->setFlash('An error occurred while loading logs', 'error');
            $this->redirect('/admin/dashboard');
        }
    }
    
    /**
     * User Management
     */
    public function users() {
        if (!$this->requireAdmin()) {
            return;
        }
        
        try {
            $userModel = new User();
            $users = $userModel->findAll([], 'username ASC');
            
            $this->data['page_title'] = 'User Management';
            $this->data['users'] = $users;
            $this->data['flash'] = $this->getFlash();
            
            $this->view('admin/users');
            
        } catch (Exception $e) {
            $this->logger->logError('User management error: ' . $e->getMessage());
            $this->setFlash('An error occurred while loading users', 'error');
            $this->redirect('/admin/dashboard');
        }
    }
    
    /**
     * Create User
     */
    public function createUser() {
        if (!$this->requireAdmin()) {
            return;
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->validateCSRF();
            
            try {
                $data = $this->getPostData(['username', 'email', 'password', 'role']);
                
                $userModel = new User();
                $validator = $userModel->validateUser($data);
                
                if ($validator->fails()) {
                    if ($this->isAjax()) {
                        $this->jsonError('Validation failed', $validator->getErrors());
                    }
                    
                    $this->setFlash('Please correct the errors and try again', 'error');
                    $this->redirect('/admin/users');
                }
                
                $user = $userModel->createUser($data);
                
                if ($user) {
                    $this->logActivity('User created', "Username: {$data['username']}, Role: {$data['role']}");
                    
                    if ($this->isAjax()) {
                        $this->jsonSuccess('User created successfully', $user);
                    }
                    
                    $this->setFlash('User created successfully', 'success');
                } else {
                    if ($this->isAjax()) {
                        $this->jsonError('Failed to create user');
                    }
                    
                    $this->setFlash('Failed to create user', 'error');
                }
                
                $this->redirect('/admin/users');
                
            } catch (Exception $e) {
                $this->logger->logError('Create user error: ' . $e->getMessage());
                
                if ($this->isAjax()) {
                    $this->jsonError('An error occurred while creating user');
                }
                
                $this->setFlash('An error occurred while creating user', 'error');
                $this->redirect('/admin/users');
            }
        }
        
        // Show create form
        $this->data['page_title'] = 'Create User';
        $this->data['flash'] = $this->getFlash();
        
        $this->view('admin/user-form');
    }
    
    /**
     * Edit User
     */
    public function editUser($id) {
        if (!$this->requireAdmin()) {
            return;
        }
        
        try {
            $userModel = new User();
            $user = $userModel->find($id);
            
            if (!$user) {
                $this->setFlash('User not found', 'error');
                $this->redirect('/admin/users');
            }
            
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $this->validateCSRF();
                
                $data = $this->getPostData(['username', 'email', 'role', 'is_active']);
                
                // Only include password if provided
                if (!empty($_POST['password'])) {
                    $data['password'] = $_POST['password'];
                }
                
                $validator = $userModel->validateUser($data, $id);
                
                if ($validator->fails()) {
                    if ($this->isAjax()) {
                        $this->jsonError('Validation failed', $validator->getErrors());
                    }
                    
                    $this->setFlash('Please correct the errors and try again', 'error');
                    $this->redirect("/admin/users/edit/$id");
                }
                
                // Update user
                if (isset($data['password'])) {
                    $userModel->updatePassword($id, $data['password']);
                    unset($data['password']);
                }
                
                $updatedUser = $userModel->update($id, $data);
                
                if ($updatedUser) {
                    $this->logActivity('User updated', "Username: {$data['username']}, ID: $id");
                    
                    if ($this->isAjax()) {
                        $this->jsonSuccess('User updated successfully', $updatedUser);
                    }
                    
                    $this->setFlash('User updated successfully', 'success');
                } else {
                    if ($this->isAjax()) {
                        $this->jsonError('Failed to update user');
                    }
                    
                    $this->setFlash('Failed to update user', 'error');
                }
                
                $this->redirect('/admin/users');
            }
            
            // Show edit form
            $this->data['page_title'] = 'Edit User';
            $this->data['user'] = $user;
            $this->data['flash'] = $this->getFlash();
            
            $this->view('admin/user-form');
            
        } catch (Exception $e) {
            $this->logger->logError('Edit user error: ' . $e->getMessage());
            $this->setFlash('An error occurred while editing user', 'error');
            $this->redirect('/admin/users');
        }
    }
    
    /**
     * Delete User
     */
    public function deleteUser($id) {
        if (!$this->requireAdmin()) {
            return;
        }
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/admin/users');
        }
        
        $this->validateCSRF();
        
        try {
            $userModel = new User();
            $user = $userModel->find($id);
            
            if (!$user) {
                if ($this->isAjax()) {
                    $this->jsonError('User not found');
                }
                
                $this->setFlash('User not found', 'error');
                $this->redirect('/admin/users');
            }
            
            // Don't allow deleting own account
            if ($id == $_SESSION['user_id']) {
                if ($this->isAjax()) {
                    $this->jsonError('Cannot delete your own account');
                }
                
                $this->setFlash('Cannot delete your own account', 'error');
                $this->redirect('/admin/users');
            }
            
            $deleted = $userModel->delete($id);
            
            if ($deleted) {
                $this->logActivity('User deleted', "Username: {$user['username']}, ID: $id");
                
                if ($this->isAjax()) {
                    $this->jsonSuccess('User deleted successfully');
                }
                
                $this->setFlash('User deleted successfully', 'success');
            } else {
                if ($this->isAjax()) {
                    $this->jsonError('Failed to delete user');
                }
                
                $this->setFlash('Failed to delete user', 'error');
            }
            
            $this->redirect('/admin/users');
            
        } catch (Exception $e) {
            $this->logger->logError('Delete user error: ' . $e->getMessage());
            
            if ($this->isAjax()) {
                $this->jsonError('An error occurred while deleting user');
            }
            
            $this->setFlash('An error occurred while deleting user', 'error');
            $this->redirect('/admin/users');
        }
    }
    
    /**
     * Archive old logs
     */
    public function archiveLogs() {
        if (!$this->requireAdmin()) {
            return;
        }
        
        try {
            $this->logger->archiveLogs();
            
            $this->logActivity('Logs archived', 'System logs archived successfully');
            
            if ($this->isAjax()) {
                $this->jsonSuccess('Logs archived successfully');
            }
            
            $this->setFlash('Logs archived successfully', 'success');
            $this->redirect('/admin/logs');
            
        } catch (Exception $e) {
            $this->logger->logError('Archive logs error: ' . $e->getMessage());
            
            if ($this->isAjax()) {
                $this->jsonError('Failed to archive logs');
            }
            
            $this->setFlash('Failed to archive logs', 'error');
            $this->redirect('/admin/logs');
        }
    }
}
?>
