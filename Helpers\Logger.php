<?php
/**
 * Logging System
 * Handles different types of logs: access, activity, error, security
 */

class Logger {
    private $db;
    private $logPath;
    
    // Log levels
    const DEBUG = 'DEBUG';
    const INFO = 'INFO';
    const WARNING = 'WARNING';
    const ERROR = 'ERROR';
    const CRITICAL = 'CRITICAL';
    
    // Log types
    const ACCESS = 'access';
    const ACTIVITY = 'activity';
    const ERROR_LOG = 'error';
    const SECURITY = 'security';
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->logPath = LOGS_PATH;
        $this->ensureLogDirectory();
    }
    
    /**
     * Ensure log directory exists
     */
    private function ensureLogDirectory() {
        if (!is_dir($this->logPath)) {
            mkdir($this->logPath, 0755, true);
        }
        
        // Create subdirectories for each log type
        $logTypes = [self::ACCESS, self::ACTIVITY, self::ERROR_LOG, self::SECURITY];
        foreach ($logTypes as $type) {
            $typeDir = $this->logPath . '/' . $type;
            if (!is_dir($typeDir)) {
                mkdir($typeDir, 0755, true);
            }
        }
    }
    
    /**
     * Log access requests
     */
    public function logAccess($method, $uri, $ipAddress, $userAgent = null) {
        $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
        $userAgent = $userAgent ?: $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        $this->log(self::ACCESS, self::INFO, "Access: $method $uri", [
            'user_id' => $userId,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'request_method' => $method,
            'request_uri' => $uri
        ]);
    }
    
    /**
     * Log user activities
     */
    public function logActivity($action, $details = '', $userId = null) {
        $userId = $userId ?: ($_SESSION['user_id'] ?? null);
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
        
        $message = "Activity: $action";
        if ($details) {
            $message .= " - $details";
        }
        
        $this->log(self::ACTIVITY, self::INFO, $message, [
            'user_id' => $userId,
            'ip_address' => $ipAddress,
            'action' => $action,
            'details' => $details
        ]);
    }
    
    /**
     * Log errors
     */
    public function logError($message, $trace = '', $level = self::ERROR) {
        $userId = $_SESSION['user_id'] ?? null;
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
        
        $this->log(self::ERROR_LOG, $level, $message, [
            'user_id' => $userId,
            'ip_address' => $ipAddress,
            'trace' => $trace
        ]);
    }
    
    /**
     * Log security events
     */
    public function logSecurity($event, $details = '', $level = self::WARNING) {
        $userId = $_SESSION['user_id'] ?? null;
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        $message = "Security: $event";
        if ($details) {
            $message .= " - $details";
        }
        
        $this->log(self::SECURITY, $level, $message, [
            'user_id' => $userId,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'event' => $event,
            'details' => $details
        ]);
    }
    
    /**
     * Main logging method
     */
    private function log($type, $level, $message, $additionalData = []) {
        try {
            // Log to database
            $this->logToDatabase($type, $level, $message, $additionalData);
            
            // Log to file
            $this->logToFile($type, $level, $message, $additionalData);
            
        } catch (Exception $e) {
            // Fallback to error log if logging fails
            error_log("Logger failed: " . $e->getMessage());
        }
    }
    
    /**
     * Log to database
     */
    private function logToDatabase($type, $level, $message, $additionalData) {
        $data = [
            'log_type' => $type,
            'level' => $level,
            'message' => $message,
            'user_id' => $additionalData['user_id'] ?? null,
            'ip_address' => $additionalData['ip_address'] ?? null,
            'user_agent' => $additionalData['user_agent'] ?? null,
            'request_method' => $additionalData['request_method'] ?? null,
            'request_uri' => $additionalData['request_uri'] ?? null,
            'additional_data' => json_encode($additionalData)
        ];
        
        $this->db->insert('system_logs', $data);
    }
    
    /**
     * Log to file
     */
    private function logToFile($type, $level, $message, $additionalData) {
        $date = date('Y-m-d');
        $filename = $this->logPath . '/' . $type . '/' . $date . '.log';
        
        $timestamp = date('Y-m-d H:i:s');
        $userId = $additionalData['user_id'] ?? 'guest';
        $ipAddress = $additionalData['ip_address'] ?? 'unknown';
        
        $logEntry = "[$timestamp] [$level] [User:$userId] [IP:$ipAddress] $message";
        
        if (!empty($additionalData)) {
            $logEntry .= ' | Data: ' . json_encode($additionalData);
        }
        
        $logEntry .= PHP_EOL;
        
        file_put_contents($filename, $logEntry, FILE_APPEND | LOCK_EX);
        
        // Check file size and rotate if necessary
        $this->rotateLogIfNeeded($filename);
    }
    
    /**
     * Rotate log file if it exceeds maximum size
     */
    private function rotateLogIfNeeded($filename) {
        if (file_exists($filename) && filesize($filename) > Config::LOG_MAX_SIZE) {
            $rotatedName = $filename . '.' . time();
            rename($filename, $rotatedName);
            
            // Compress the rotated file
            if (function_exists('gzopen')) {
                $this->compressFile($rotatedName);
            }
        }
    }
    
    /**
     * Compress log file
     */
    private function compressFile($filename) {
        $gzFilename = $filename . '.gz';
        $file = fopen($filename, 'rb');
        $gzFile = gzopen($gzFilename, 'wb9');
        
        while (!feof($file)) {
            gzwrite($gzFile, fread($file, 8192));
        }
        
        fclose($file);
        gzclose($gzFile);
        unlink($filename);
    }
    
    /**
     * Archive old logs
     */
    public function archiveLogs() {
        $archivePath = $this->logPath . '/archive';
        if (!is_dir($archivePath)) {
            mkdir($archivePath, 0755, true);
        }
        
        $cutoffDate = date('Y-m-d', strtotime('-' . Config::LOG_ARCHIVE_DAYS . ' days'));
        $logTypes = [self::ACCESS, self::ACTIVITY, self::ERROR_LOG, self::SECURITY];
        
        foreach ($logTypes as $type) {
            $typeDir = $this->logPath . '/' . $type;
            if (!is_dir($typeDir)) continue;
            
            $files = glob($typeDir . '/*.log*');
            foreach ($files as $file) {
                $fileDate = basename($file, '.log');
                if (strpos($fileDate, '.') !== false) {
                    $fileDate = substr($fileDate, 0, strpos($fileDate, '.'));
                }
                
                if ($fileDate < $cutoffDate) {
                    $archiveFile = $archivePath . '/' . $type . '_' . basename($file);
                    rename($file, $archiveFile);
                }
            }
        }
        
        // Clean database logs older than archive days
        $this->cleanOldDatabaseLogs();
    }
    
    /**
     * Clean old database logs
     */
    private function cleanOldDatabaseLogs() {
        $cutoffDate = date('Y-m-d H:i:s', strtotime('-' . Config::LOG_ARCHIVE_DAYS . ' days'));
        $this->db->delete('system_logs', 'created_at < :cutoff', ['cutoff' => $cutoffDate]);
    }
    
    /**
     * Get logs from database
     */
    public function getLogs($type = null, $level = null, $limit = 100, $offset = 0) {
        $sql = "SELECT * FROM system_logs WHERE 1=1";
        $params = [];
        
        if ($type) {
            $sql .= " AND log_type = :type";
            $params['type'] = $type;
        }
        
        if ($level) {
            $sql .= " AND level = :level";
            $params['level'] = $level;
        }
        
        $sql .= " ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        return $this->db->fetchAll($sql, $params);
    }
}
?>
