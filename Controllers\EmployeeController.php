<?php
/**
 * Employee Controller
 * Handles employee management for admin/HR
 */

require_once 'BaseController.php';

class EmployeeController extends BaseController {
    private $employeeModel;
    
    public function __construct() {
        parent::__construct();
        
        // Require HR or Admin role
        if (!$this->requireHR()) {
            return;
        }
        
        $this->employeeModel = new Employee();
    }
    
    /**
     * List all employees
     */
    public function index() {
        try {
            $page = (int)($_GET['page'] ?? 1);
            $search = $_GET['search'] ?? '';
            $department = $_GET['department'] ?? '';
            $status = $_GET['status'] ?? '';
            
            if ($search) {
                $filters = [];
                if ($department) $filters['department'] = $department;
                if ($status) $filters['status'] = $status;
                
                $employees = $this->employeeModel->search($search, $filters);
                $pagination = null;
            } else {
                $conditions = [];
                if ($department) $conditions['e.department'] = $department;
                if ($status) $conditions['e.status'] = $status;
                
                $result = $this->paginate(
                    "SELECT e.*, u.username, u.role, u.is_active, u.last_login,
                            m.first_name as manager_first_name, m.last_name as manager_last_name
                     FROM employees e 
                     LEFT JOIN users u ON e.user_id = u.id 
                     LEFT JOIN employees m ON e.manager_id = m.id",
                    $conditions,
                    $page
                );
                
                $employees = $result['data'];
                $pagination = $result['pagination'];
            }
            
            // Get filter options
            $departments = $this->employeeModel->getDepartments();
            $positions = $this->employeeModel->getPositions();
            
            $this->data['page_title'] = 'Employee Management';
            $this->data['employees'] = $employees;
            $this->data['pagination'] = $pagination;
            $this->data['departments'] = $departments;
            $this->data['positions'] = $positions;
            $this->data['search'] = $search;
            $this->data['filter_department'] = $department;
            $this->data['filter_status'] = $status;
            $this->data['flash'] = $this->getFlash();
            
            $this->view('admin/employees/index');
            
        } catch (Exception $e) {
            $this->logger->logError('Employee list error: ' . $e->getMessage());
            $this->setFlash('An error occurred while loading employees', 'error');
            $this->view('admin/employees/index');
        }
    }
    
    /**
     * Show employee details
     */
    public function show($id) {
        try {
            $employee = $this->employeeModel->getWithUser($id);
            
            if (!$employee) {
                if ($this->isAjax()) {
                    $this->jsonError('Employee not found', [], 404);
                }
                
                $this->setFlash('Employee not found', 'error');
                $this->redirect('/admin/employees');
            }
            
            // Get additional data
            $leaveModel = new LeaveRequest();
            $salaryModel = new SalaryRecord();
            
            $leaveBalance = $leaveModel->getAllLeaveBalances($id);
            $recentLeaves = $leaveModel->getByEmployee($id, date('Y'));
            $recentSalaries = $salaryModel->getByEmployee($id, 6);
            
            if ($this->isAjax()) {
                $this->jsonSuccess('Employee details', [
                    'employee' => $employee,
                    'leave_balance' => $leaveBalance,
                    'recent_leaves' => $recentLeaves,
                    'recent_salaries' => $recentSalaries
                ]);
            }
            
            $this->data['page_title'] = 'Employee Details';
            $this->data['employee'] = $employee;
            $this->data['leave_balance'] = $leaveBalance;
            $this->data['recent_leaves'] = $recentLeaves;
            $this->data['recent_salaries'] = $recentSalaries;
            
            $this->view('admin/employees/show');
            
        } catch (Exception $e) {
            $this->logger->logError('Employee show error: ' . $e->getMessage());
            
            if ($this->isAjax()) {
                $this->jsonError('An error occurred while loading employee details');
            }
            
            $this->setFlash('An error occurred while loading employee details', 'error');
            $this->redirect('/admin/employees');
        }
    }
    
    /**
     * Show create employee form
     */
    public function create() {
        $this->data['page_title'] = 'Add New Employee';
        $this->data['departments'] = $this->employeeModel->getDepartments();
        $this->data['positions'] = $this->employeeModel->getPositions();
        $this->data['managers'] = $this->employeeModel->findAll(['status' => 'active'], 'first_name, last_name');
        $this->data['flash'] = $this->getFlash();
        
        $this->view('admin/employees/form');
    }
    
    /**
     * Store new employee
     */
    public function store() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/admin/employees/create');
        }
        
        $this->validateCSRF();
        
        try {
            $employeeData = $this->getPostData([
                'employee_code', 'first_name', 'last_name', 'email', 'phone',
                'address', 'birth_date', 'hire_date', 'position', 'department',
                'manager_id', 'salary', 'line_user_id'
            ]);
            
            $userData = $this->getPostData(['username', 'password', 'role']);
            $userData['email'] = $employeeData['email'];
            
            // Validate employee data
            $validator = $this->employeeModel->validateEmployee($employeeData);
            
            if ($validator->fails()) {
                if ($this->isAjax()) {
                    $this->jsonError('Validation failed', $validator->getErrors());
                }
                
                $this->setFlash('Please correct the errors and try again', 'error');
                $this->redirect('/admin/employees/create');
            }
            
            // Validate user data
            $userModel = new User();
            $userValidator = $userModel->validateUser($userData);
            
            if ($userValidator->fails()) {
                if ($this->isAjax()) {
                    $this->jsonError('User validation failed', $userValidator->getErrors());
                }
                
                $this->setFlash('Please correct the user data errors and try again', 'error');
                $this->redirect('/admin/employees/create');
            }
            
            // Handle file upload
            if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
                $filename = $this->handleFileUpload('profile_image', 'Assets/uploads/profiles/');
                $employeeData['profile_image'] = $filename;
            }
            
            // Create employee with user account
            $employee = $this->employeeModel->createWithUser($employeeData, $userData);
            
            if ($employee) {
                $this->logActivity('Employee created', "Employee: {$employeeData['first_name']} {$employeeData['last_name']} ({$employeeData['employee_code']})");
                
                if ($this->isAjax()) {
                    $this->jsonSuccess('Employee created successfully', $employee);
                }
                
                $this->setFlash('Employee created successfully', 'success');
                $this->redirect('/admin/employees');
            } else {
                if ($this->isAjax()) {
                    $this->jsonError('Failed to create employee');
                }
                
                $this->setFlash('Failed to create employee', 'error');
                $this->redirect('/admin/employees/create');
            }
            
        } catch (Exception $e) {
            $this->logger->logError('Employee create error: ' . $e->getMessage());
            
            if ($this->isAjax()) {
                $this->jsonError('An error occurred while creating employee');
            }
            
            $this->setFlash('An error occurred while creating employee', 'error');
            $this->redirect('/admin/employees/create');
        }
    }
    
    /**
     * Show edit employee form
     */
    public function edit($id) {
        try {
            $employee = $this->employeeModel->getWithUser($id);
            
            if (!$employee) {
                $this->setFlash('Employee not found', 'error');
                $this->redirect('/admin/employees');
            }
            
            $this->data['page_title'] = 'Edit Employee';
            $this->data['employee'] = $employee;
            $this->data['departments'] = $this->employeeModel->getDepartments();
            $this->data['positions'] = $this->employeeModel->getPositions();
            $this->data['managers'] = $this->employeeModel->findAll(['status' => 'active'], 'first_name, last_name');
            $this->data['flash'] = $this->getFlash();
            
            $this->view('admin/employees/form');
            
        } catch (Exception $e) {
            $this->logger->logError('Employee edit form error: ' . $e->getMessage());
            $this->setFlash('An error occurred while loading employee data', 'error');
            $this->redirect('/admin/employees');
        }
    }
    
    /**
     * Update employee
     */
    public function update($id) {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect("/admin/employees/edit/$id");
        }
        
        $this->validateCSRF();
        
        try {
            $employee = $this->employeeModel->find($id);
            
            if (!$employee) {
                if ($this->isAjax()) {
                    $this->jsonError('Employee not found', [], 404);
                }
                
                $this->setFlash('Employee not found', 'error');
                $this->redirect('/admin/employees');
            }
            
            $employeeData = $this->getPostData([
                'employee_code', 'first_name', 'last_name', 'email', 'phone',
                'address', 'birth_date', 'hire_date', 'position', 'department',
                'manager_id', 'salary', 'status', 'line_user_id'
            ]);
            
            // Validate employee data
            $validator = $this->employeeModel->validateEmployee($employeeData, $id);
            
            if ($validator->fails()) {
                if ($this->isAjax()) {
                    $this->jsonError('Validation failed', $validator->getErrors());
                }
                
                $this->setFlash('Please correct the errors and try again', 'error');
                $this->redirect("/admin/employees/edit/$id");
            }
            
            // Handle file upload
            if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
                $filename = $this->handleFileUpload('profile_image', 'Assets/uploads/profiles/');
                $employeeData['profile_image'] = $filename;
                
                // Delete old profile image
                if ($employee['profile_image'] && file_exists('Assets/uploads/profiles/' . $employee['profile_image'])) {
                    unlink('Assets/uploads/profiles/' . $employee['profile_image']);
                }
            }
            
            // Update employee
            $updatedEmployee = $this->employeeModel->update($id, $employeeData);
            
            if ($updatedEmployee) {
                $this->logActivity('Employee updated', "Employee: {$employeeData['first_name']} {$employeeData['last_name']} ({$employeeData['employee_code']})");
                
                if ($this->isAjax()) {
                    $this->jsonSuccess('Employee updated successfully', $updatedEmployee);
                }
                
                $this->setFlash('Employee updated successfully', 'success');
                $this->redirect('/admin/employees');
            } else {
                if ($this->isAjax()) {
                    $this->jsonError('Failed to update employee');
                }
                
                $this->setFlash('Failed to update employee', 'error');
                $this->redirect("/admin/employees/edit/$id");
            }
            
        } catch (Exception $e) {
            $this->logger->logError('Employee update error: ' . $e->getMessage());
            
            if ($this->isAjax()) {
                $this->jsonError('An error occurred while updating employee');
            }
            
            $this->setFlash('An error occurred while updating employee', 'error');
            $this->redirect("/admin/employees/edit/$id");
        }
    }
    
    /**
     * Delete employee
     */
    public function delete($id) {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/admin/employees');
        }
        
        $this->validateCSRF();
        
        try {
            $employee = $this->employeeModel->find($id);
            
            if (!$employee) {
                if ($this->isAjax()) {
                    $this->jsonError('Employee not found', [], 404);
                }
                
                $this->setFlash('Employee not found', 'error');
                $this->redirect('/admin/employees');
            }
            
            // Instead of deleting, mark as terminated
            $updated = $this->employeeModel->update($id, ['status' => 'terminated']);
            
            if ($updated) {
                $this->logActivity('Employee terminated', "Employee: {$employee['first_name']} {$employee['last_name']} ({$employee['employee_code']})");
                
                if ($this->isAjax()) {
                    $this->jsonSuccess('Employee terminated successfully');
                }
                
                $this->setFlash('Employee terminated successfully', 'success');
            } else {
                if ($this->isAjax()) {
                    $this->jsonError('Failed to terminate employee');
                }
                
                $this->setFlash('Failed to terminate employee', 'error');
            }
            
            $this->redirect('/admin/employees');
            
        } catch (Exception $e) {
            $this->logger->logError('Employee delete error: ' . $e->getMessage());
            
            if ($this->isAjax()) {
                $this->jsonError('An error occurred while terminating employee');
            }
            
            $this->setFlash('An error occurred while terminating employee', 'error');
            $this->redirect('/admin/employees');
        }
    }
    
    /**
     * Export employees to CSV
     */
    public function export() {
        try {
            $employees = $this->employeeModel->getAllWithUsers();
            
            $filename = 'employees_' . date('Y-m-d_H-i-s') . '.csv';
            
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            
            $output = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($output, [
                'Employee Code', 'First Name', 'Last Name', 'Email', 'Phone',
                'Department', 'Position', 'Hire Date', 'Salary', 'Status', 'Manager'
            ]);
            
            // CSV data
            foreach ($employees as $employee) {
                fputcsv($output, [
                    $employee['employee_code'],
                    $employee['first_name'],
                    $employee['last_name'],
                    $employee['email'],
                    $employee['phone'],
                    $employee['department'],
                    $employee['position'],
                    $employee['hire_date'],
                    $employee['salary'],
                    $employee['status'],
                    $employee['manager_first_name'] . ' ' . $employee['manager_last_name']
                ]);
            }
            
            fclose($output);
            
            $this->logActivity('Employees exported', 'Employee data exported to CSV');
            
        } catch (Exception $e) {
            $this->logger->logError('Employee export error: ' . $e->getMessage());
            $this->setFlash('An error occurred while exporting employees', 'error');
            $this->redirect('/admin/employees');
        }
    }
}
?>
