<?php
/**
 * Salary Record Model
 */

require_once 'BaseModel.php';

class SalaryRecord extends BaseModel {
    protected $table = 'salary_records';
    protected $fillable = [
        'employee_id', 'base_salary', 'allowances', 'overtime_pay', 
        'deductions', 'net_salary', 'pay_period_start', 'pay_period_end', 
        'pay_date', 'status', 'notes', 'created_by'
    ];
    
    /**
     * Get salary records with employee data
     */
    public function getWithEmployee($conditions = [], $orderBy = 'sr.pay_period_start DESC') {
        $sql = "SELECT sr.*, e.first_name, e.last_name, e.employee_code, e.department, e.position
                FROM salary_records sr
                JOIN employees e ON sr.employee_id = e.id";
        
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $field => $value) {
                $whereClause[] = "$field = :$field";
                $params[$field] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY $orderBy";
        }
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get salary records for employee
     */
    public function getByEmployee($employeeId, $limit = null) {
        $conditions = ['sr.employee_id' => $employeeId];
        $orderBy = 'sr.pay_period_start DESC';
        
        if ($limit) {
            $orderBy .= " LIMIT $limit";
        }
        
        return $this->getWithEmployee($conditions, $orderBy);
    }
    
    /**
     * Get current month salary records
     */
    public function getCurrentMonth() {
        $sql = "SELECT sr.*, e.first_name, e.last_name, e.employee_code, e.department
                FROM salary_records sr
                JOIN employees e ON sr.employee_id = e.id
                WHERE YEAR(sr.pay_period_start) = YEAR(CURDATE()) 
                AND MONTH(sr.pay_period_start) = MONTH(CURDATE())
                ORDER BY e.first_name, e.last_name";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Get salary records by period
     */
    public function getByPeriod($startDate, $endDate) {
        $sql = "SELECT sr.*, e.first_name, e.last_name, e.employee_code, e.department
                FROM salary_records sr
                JOIN employees e ON sr.employee_id = e.id
                WHERE sr.pay_period_start >= :start_date 
                AND sr.pay_period_end <= :end_date
                ORDER BY sr.pay_period_start DESC, e.first_name";
        
        return $this->db->fetchAll($sql, [
            'start_date' => $startDate,
            'end_date' => $endDate
        ]);
    }
    
    /**
     * Calculate net salary
     */
    public function calculateNetSalary($baseSalary, $allowances = 0, $overtimePay = 0, $deductions = 0) {
        return $baseSalary + $allowances + $overtimePay - $deductions;
    }
    
    /**
     * Generate salary slip
     */
    public function generateSalarySlip($id) {
        $sql = "SELECT sr.*, e.first_name, e.last_name, e.employee_code, 
                       e.department, e.position, e.hire_date
                FROM salary_records sr
                JOIN employees e ON sr.employee_id = e.id
                WHERE sr.id = :id";
        
        $salaryRecord = $this->db->fetchOne($sql, ['id' => $id]);
        
        if (!$salaryRecord) {
            return null;
        }
        
        // Encrypt sensitive salary data
        $encryptedData = [
            'base_salary' => Security::encrypt($salaryRecord['base_salary']),
            'allowances' => Security::encrypt($salaryRecord['allowances']),
            'overtime_pay' => Security::encrypt($salaryRecord['overtime_pay']),
            'deductions' => Security::encrypt($salaryRecord['deductions']),
            'net_salary' => Security::encrypt($salaryRecord['net_salary'])
        ];
        
        $salaryRecord['encrypted_data'] = $encryptedData;
        
        return $salaryRecord;
    }
    
    /**
     * Get salary statistics
     */
    public function getStatistics($year = null) {
        $year = $year ?: date('Y');
        
        $stats = [];
        
        // Total payroll for the year
        $sql = "SELECT SUM(net_salary) as total_payroll, COUNT(*) as total_records
                FROM salary_records 
                WHERE YEAR(pay_period_start) = :year AND status = 'paid'";
        $result = $this->db->fetchOne($sql, ['year' => $year]);
        $stats['total_payroll'] = $result['total_payroll'] ?: 0;
        $stats['total_records'] = $result['total_records'] ?: 0;
        
        // Average salary
        $sql = "SELECT AVG(net_salary) as avg_salary
                FROM salary_records 
                WHERE YEAR(pay_period_start) = :year AND status = 'paid'";
        $result = $this->db->fetchOne($sql, ['year' => $year]);
        $stats['average_salary'] = $result['avg_salary'] ?: 0;
        
        // Monthly breakdown
        $sql = "SELECT MONTH(pay_period_start) as month, 
                       SUM(net_salary) as total, 
                       COUNT(*) as count
                FROM salary_records 
                WHERE YEAR(pay_period_start) = :year AND status = 'paid'
                GROUP BY MONTH(pay_period_start)
                ORDER BY month";
        $stats['monthly_breakdown'] = $this->db->fetchAll($sql, ['year' => $year]);
        
        // By department
        $sql = "SELECT e.department, 
                       SUM(sr.net_salary) as total, 
                       COUNT(*) as count,
                       AVG(sr.net_salary) as average
                FROM salary_records sr
                JOIN employees e ON sr.employee_id = e.id
                WHERE YEAR(sr.pay_period_start) = :year AND sr.status = 'paid'
                AND e.department IS NOT NULL
                GROUP BY e.department
                ORDER BY total DESC";
        $stats['by_department'] = $this->db->fetchAll($sql, ['year' => $year]);
        
        return $stats;
    }
    
    /**
     * Validate salary record data
     */
    public function validateSalaryRecord($data, $id = null) {
        $rules = [
            'employee_id' => 'required|exists:employees,id',
            'base_salary' => 'required|numeric',
            'allowances' => 'numeric',
            'overtime_pay' => 'numeric',
            'deductions' => 'numeric',
            'pay_period_start' => 'required|date',
            'pay_period_end' => 'required|date',
            'status' => 'required'
        ];
        
        return $this->validate($data, $rules);
    }
    
    /**
     * Create salary record with calculated net salary
     */
    public function createSalaryRecord($data) {
        // Calculate net salary
        $data['net_salary'] = $this->calculateNetSalary(
            $data['base_salary'],
            $data['allowances'] ?? 0,
            $data['overtime_pay'] ?? 0,
            $data['deductions'] ?? 0
        );
        
        return $this->create($data);
    }
    
    /**
     * Update salary record with recalculated net salary
     */
    public function updateSalaryRecord($id, $data) {
        // Recalculate net salary if any salary components changed
        if (isset($data['base_salary']) || isset($data['allowances']) || 
            isset($data['overtime_pay']) || isset($data['deductions'])) {
            
            $current = $this->find($id);
            if ($current) {
                $data['net_salary'] = $this->calculateNetSalary(
                    $data['base_salary'] ?? $current['base_salary'],
                    $data['allowances'] ?? $current['allowances'],
                    $data['overtime_pay'] ?? $current['overtime_pay'],
                    $data['deductions'] ?? $current['deductions']
                );
            }
        }
        
        return $this->update($id, $data);
    }
    
    /**
     * Check if salary record exists for employee in period
     */
    public function existsForPeriod($employeeId, $startDate, $endDate, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM salary_records 
                WHERE employee_id = :employee_id 
                AND ((pay_period_start <= :start_date AND pay_period_end >= :start_date)
                     OR (pay_period_start <= :end_date AND pay_period_end >= :end_date)
                     OR (pay_period_start >= :start_date AND pay_period_end <= :end_date))";
        
        $params = [
            'employee_id' => $employeeId,
            'start_date' => $startDate,
            'end_date' => $endDate
        ];
        
        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $result = $this->db->fetchOne($sql, $params);
        return $result['count'] > 0;
    }
}
?>
