<?php
/**
 * Authentication Controller
 * Handles login, logout, and authentication
 */

require_once 'BaseController.php';

class AuthController extends BaseController {
    
    /**
     * Show login form
     */
    public function login() {
        // Redirect if already logged in
        if (Security::isLoggedIn()) {
            $this->redirect('/dashboard');
        }
        
        $this->data['page_title'] = 'Login';
        $this->data['flash'] = $this->getFlash();
        $this->data['login_message'] = $_SESSION['login_message'] ?? null;
        unset($_SESSION['login_message']);
        
        $this->view('auth/login');
    }
    
    /**
     * Process login
     */
    public function processLogin() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/auth/login');
        }
        
        // Validate CSRF token
        $this->validateCSRF();
        
        // Get and sanitize input
        $data = $this->getPostData(['username', 'password', 'remember']);
        
        // Validate input
        $validator = Validator::make($data, [
            'username' => 'required',
            'password' => 'required'
        ]);
        
        if ($validator->fails()) {
            if ($this->isAjax()) {
                $this->jsonError('Validation failed', $validator->getErrors());
            }
            
            $this->setFlash('Please fill in all required fields', 'error');
            $this->redirect('/auth/login');
        }
        
        $username = $data['username'];
        $password = $data['password'];
        $remember = isset($data['remember']);
        
        // Check rate limiting
        $ipAddress = $_SERVER['REMOTE_ADDR'];
        $loginCheck = Security::checkLoginAttempts($ipAddress);
        
        if (!$loginCheck['allowed']) {
            $remainingTime = $loginCheck['remaining_time'] ?? 0;
            $message = "Too many failed login attempts. Please try again in " . ceil($remainingTime / 60) . " minutes.";
            
            if ($this->isAjax()) {
                $this->jsonError($message, [], 429);
            }
            
            $this->setFlash($message, 'error');
            $this->redirect('/auth/login');
        }
        
        try {
            // Find user
            $user = $this->db->fetchOne(
                "SELECT * FROM users WHERE (username = :username OR email = :username) AND is_active = 1",
                ['username' => $username]
            );
            
            if (!$user || !Security::verifyPassword($password, $user['password_hash'])) {
                // Record failed login attempt
                Security::recordFailedLogin($ipAddress);
                $this->logger->logSecurity('Failed login attempt', "Username: $username, IP: $ipAddress");
                
                $message = 'Invalid username or password';
                
                if ($this->isAjax()) {
                    $this->jsonError($message);
                }
                
                $this->setFlash($message, 'error');
                $this->redirect('/auth/login');
            }
            
            // Check if account is locked
            if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
                $message = 'Account is temporarily locked. Please try again later.';
                
                if ($this->isAjax()) {
                    $this->jsonError($message);
                }
                
                $this->setFlash($message, 'error');
                $this->redirect('/auth/login');
            }
            
            // Clear failed login attempts
            Security::clearLoginAttempts($ipAddress);
            
            // Update last login and clear lock
            $this->db->update('users', [
                'last_login' => date('Y-m-d H:i:s'),
                'failed_login_attempts' => 0,
                'locked_until' => null
            ], 'id = :id', ['id' => $user['id']]);
            
            // Set session data
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['last_activity'] = time();
            
            // Set remember me cookie if requested
            if ($remember) {
                $token = Security::generateToken();
                setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', true, true); // 30 days
                
                // Store token in database (you might want to create a remember_tokens table)
                // For now, we'll skip this implementation
            }
            
            // Log successful login
            $this->logger->logActivity('User login', "User: {$user['username']}");
            $this->logger->logSecurity('Successful login', "User: {$user['username']}, IP: $ipAddress");
            
            // Determine redirect URL based on role
            $redirectUrl = '/dashboard';
            if ($user['role'] === 'admin' || $user['role'] === 'hr') {
                $redirectUrl = '/admin/dashboard';
            }
            
            if ($this->isAjax()) {
                $this->jsonSuccess('Login successful', ['redirect' => $redirectUrl]);
            }
            
            $this->redirect($redirectUrl);
            
        } catch (Exception $e) {
            $this->logger->logError('Login error: ' . $e->getMessage());
            
            $message = 'An error occurred during login. Please try again.';
            
            if ($this->isAjax()) {
                $this->jsonError($message);
            }
            
            $this->setFlash($message, 'error');
            $this->redirect('/auth/login');
        }
    }
    
    /**
     * Logout user
     */
    public function logout() {
        if (Security::isLoggedIn()) {
            $this->logger->logActivity('User logout', "User: {$_SESSION['username']}");
        }
        
        // Clear remember me cookie
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/', '', true, true);
        }
        
        Security::logout();
        
        $this->setFlash('You have been logged out successfully', 'success');
        $this->redirect('/auth/login');
    }
    
    /**
     * Show forgot password form
     */
    public function forgotPassword() {
        $this->data['page_title'] = 'Forgot Password';
        $this->data['flash'] = $this->getFlash();
        
        $this->view('auth/forgot-password');
    }
    
    /**
     * Process forgot password
     */
    public function processForgotPassword() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/auth/forgot-password');
        }
        
        $this->validateCSRF();
        
        $data = $this->getPostData(['email']);
        
        $validator = Validator::make($data, [
            'email' => 'required|email'
        ]);
        
        if ($validator->fails()) {
            if ($this->isAjax()) {
                $this->jsonError('Validation failed', $validator->getErrors());
            }
            
            $this->setFlash('Please enter a valid email address', 'error');
            $this->redirect('/auth/forgot-password');
        }
        
        $email = $data['email'];
        
        try {
            // Check if user exists
            $user = $this->db->fetchOne(
                "SELECT * FROM users WHERE email = :email AND is_active = 1",
                ['email' => $email]
            );
            
            if ($user) {
                // Generate reset token
                $token = Security::generateToken();
                $expires = date('Y-m-d H:i:s', time() + 3600); // 1 hour
                
                // Store reset token (you might want to create a password_resets table)
                // For now, we'll just log it
                $this->logger->logActivity('Password reset requested', "Email: $email, Token: $token");
                
                // In a real application, you would send an email here
                // For now, we'll just show a success message
            }
            
            // Always show success message to prevent email enumeration
            $message = 'If an account with that email exists, a password reset link has been sent.';
            
            if ($this->isAjax()) {
                $this->jsonSuccess($message);
            }
            
            $this->setFlash($message, 'success');
            $this->redirect('/auth/login');
            
        } catch (Exception $e) {
            $this->logger->logError('Forgot password error: ' . $e->getMessage());
            
            $message = 'An error occurred. Please try again.';
            
            if ($this->isAjax()) {
                $this->jsonError($message);
            }
            
            $this->setFlash($message, 'error');
            $this->redirect('/auth/forgot-password');
        }
    }
    
    /**
     * Check authentication status (for AJAX)
     */
    public function checkAuth() {
        $isLoggedIn = Security::isLoggedIn();
        $user = $isLoggedIn ? $this->auth->getCurrentUser() : null;
        
        $this->json([
            'authenticated' => $isLoggedIn,
            'user' => $user
        ]);
    }
}
?>
