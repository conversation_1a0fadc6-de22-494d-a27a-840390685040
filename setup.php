<?php
/**
 * HRC Setup and Installation Helper
 */

// Check if already installed
if (file_exists('INSTALLED.lock')) {
    die('System is already installed. Delete INSTALLED.lock file to run setup again.');
}

$step = $_GET['step'] ?? 1;
$errors = [];
$success = [];

?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HRC Setup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .setup-container { max-width: 800px; margin: 2rem auto; }
        .card { border: none; border-radius: 15px; box-shadow: 0 15px 35px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="container setup-container">
        <div class="card">
            <div class="card-header bg-primary text-white text-center">
                <h3><i class="bi bi-gear"></i> HRC System Setup</h3>
                <p class="mb-0">Human Resources Center Installation</p>
            </div>
            
            <div class="card-body">
                <!-- Progress Bar -->
                <div class="progress mb-4">
                    <div class="progress-bar" style="width: <?= ($step / 4) * 100 ?>%"></div>
                </div>
                
                <?php if ($step == 1): ?>
                <!-- Step 1: System Check -->
                <h4>Step 1: System Requirements Check</h4>
                
                <?php
                // Check PHP version
                if (version_compare(PHP_VERSION, '7.4.0', '>=')) {
                    $success[] = "PHP Version: " . PHP_VERSION . " ✅";
                } else {
                    $errors[] = "PHP 7.4+ required. Current: " . PHP_VERSION;
                }
                
                // Check extensions
                $required_extensions = ['pdo', 'pdo_mysql', 'openssl', 'json', 'mbstring'];
                foreach ($required_extensions as $ext) {
                    if (extension_loaded($ext)) {
                        $success[] = "Extension $ext: Loaded ✅";
                    } else {
                        $errors[] = "Extension $ext: Missing ❌";
                    }
                }
                
                // Check directories
                $directories = ['Assets', 'Logs', 'Config'];
                foreach ($directories as $dir) {
                    if (is_dir($dir) && is_writable($dir)) {
                        $success[] = "Directory $dir: Writable ✅";
                    } else {
                        $errors[] = "Directory $dir: Not writable ❌";
                    }
                }
                
                // Check mod_rewrite
                if (function_exists('apache_get_modules')) {
                    $modules = apache_get_modules();
                    if (in_array('mod_rewrite', $modules)) {
                        $success[] = "mod_rewrite: Enabled ✅";
                    } else {
                        $errors[] = "mod_rewrite: Disabled ❌";
                    }
                }
                ?>
                
                <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <h5>✅ Requirements Met:</h5>
                    <?php foreach ($success as $item): ?>
                        <div><?= $item ?></div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <h5>❌ Issues Found:</h5>
                    <?php foreach ($errors as $error): ?>
                        <div><?= $error ?></div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
                
                <div class="text-end">
                    <?php if (empty($errors)): ?>
                        <a href="?step=2" class="btn btn-primary">Next: Database Setup</a>
                    <?php else: ?>
                        <button class="btn btn-secondary" disabled>Fix issues first</button>
                    <?php endif; ?>
                </div>
                
                <?php elseif ($step == 2): ?>
                <!-- Step 2: Database Setup -->
                <h4>Step 2: Database Configuration</h4>
                
                <?php
                if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                    $host = $_POST['host'] ?? 'localhost';
                    $dbname = $_POST['dbname'] ?? 'hrc_database';
                    $username = $_POST['username'] ?? 'root';
                    $password = $_POST['password'] ?? '';
                    
                    try {
                        $pdo = new PDO("mysql:host=$host", $username, $password);
                        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                        
                        // Create database
                        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                        $success[] = "Database created successfully";
                        
                        // Update config file
                        $configContent = file_get_contents('Config/Database.php');
                        $configContent = str_replace("private \$host = 'localhost';", "private \$host = '$host';", $configContent);
                        $configContent = str_replace("private \$dbname = 'hrc_database';", "private \$dbname = '$dbname';", $configContent);
                        $configContent = str_replace("private \$username = 'root';", "private \$username = '$username';", $configContent);
                        $configContent = str_replace("private \$password = '';", "private \$password = '$password';", $configContent);
                        
                        file_put_contents('Config/Database.php', $configContent);
                        $success[] = "Database configuration updated";
                        
                        echo '<div class="alert alert-success">Database setup completed! <a href="?step=3" class="btn btn-sm btn-primary ms-2">Next Step</a></div>';
                        
                    } catch (Exception $e) {
                        $errors[] = "Database connection failed: " . $e->getMessage();
                    }
                }
                ?>
                
                <?php if (empty($success)): ?>
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Database Host</label>
                                <input type="text" class="form-control" name="host" value="localhost" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Database Name</label>
                                <input type="text" class="form-control" name="dbname" value="hrc_database" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Username</label>
                                <input type="text" class="form-control" name="username" value="root" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Password</label>
                                <input type="password" class="form-control" name="password">
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-end">
                        <button type="submit" class="btn btn-primary">Create Database</button>
                    </div>
                </form>
                <?php endif; ?>
                
                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <?php foreach ($errors as $error): ?>
                        <div><?= $error ?></div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
                
                <?php elseif ($step == 3): ?>
                <!-- Step 3: Import Schema -->
                <h4>Step 3: Database Schema Import</h4>
                
                <?php
                if (isset($_GET['import'])) {
                    try {
                        require_once 'Config/Database.php';
                        $db = Database::getInstance();
                        
                        // Read and execute schema
                        $schema = file_get_contents('Config/database_schema.sql');
                        $statements = explode(';', $schema);
                        
                        foreach ($statements as $statement) {
                            $statement = trim($statement);
                            if (!empty($statement)) {
                                $db->query($statement);
                            }
                        }
                        
                        $success[] = "Database schema imported successfully";
                        
                        // Import sample data
                        if (file_exists('Config/sample_data.sql')) {
                            $sampleData = file_get_contents('Config/sample_data.sql');
                            $statements = explode(';', $sampleData);
                            
                            foreach ($statements as $statement) {
                                $statement = trim($statement);
                                if (!empty($statement)) {
                                    $db->query($statement);
                                }
                            }
                            $success[] = "Sample data imported successfully";
                        }
                        
                    } catch (Exception $e) {
                        $errors[] = "Schema import failed: " . $e->getMessage();
                    }
                }
                ?>
                
                <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <?php foreach ($success as $item): ?>
                        <div>✅ <?= $item ?></div>
                    <?php endforeach; ?>
                    <a href="?step=4" class="btn btn-primary mt-2">Final Step</a>
                </div>
                <?php else: ?>
                <div class="alert alert-info">
                    <p>Ready to import database schema and sample data.</p>
                    <p><strong>This will create all necessary tables and insert demo data.</strong></p>
                </div>
                
                <div class="text-end">
                    <a href="?step=3&import=1" class="btn btn-primary">Import Schema & Data</a>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <?php foreach ($errors as $error): ?>
                        <div>❌ <?= $error ?></div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
                
                <?php elseif ($step == 4): ?>
                <!-- Step 4: Complete -->
                <h4>Step 4: Installation Complete!</h4>
                
                <?php
                // Create lock file
                file_put_contents('INSTALLED.lock', date('Y-m-d H:i:s'));
                ?>
                
                <div class="alert alert-success">
                    <h5>🎉 Installation Completed Successfully!</h5>
                    <p>Your HRC system is now ready to use.</p>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5>Default Login Credentials</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <strong>Admin:</strong><br>
                                Username: <code>admin</code><br>
                                Password: <code>admin123</code>
                            </div>
                            <div class="col-md-4">
                                <strong>HR Manager:</strong><br>
                                Username: <code>hr_manager</code><br>
                                Password: <code>admin123</code>
                            </div>
                            <div class="col-md-4">
                                <strong>Employee:</strong><br>
                                Username: <code>john.doe</code><br>
                                Password: <code>admin123</code>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-warning mt-3">
                    <strong>Important Security Notes:</strong>
                    <ul class="mb-0">
                        <li>Change default passwords immediately</li>
                        <li>Update encryption key in Config/Config.php</li>
                        <li>Delete setup.php and test.php files</li>
                        <li>Set proper file permissions</li>
                    </ul>
                </div>
                
                <div class="text-center mt-4">
                    <a href="index.php" class="btn btn-primary btn-lg">Launch HRC System</a>
                </div>
                
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
